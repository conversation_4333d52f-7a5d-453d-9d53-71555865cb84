'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, Package } from 'lucide-react';
import { 
  ProductFormSections,
  GeneralSection,
  PricingSection,
  RestrictionsSection,
  AvailabilitySection,
  StockSection
} from '@/components/ui/product-form-sections';
import { 
  BasicInfoFields, 
  PricingFields, 
  RestrictionsFields, 
  StockFields 
} from '@/components/forms/product-form-fields';
import { ImageUpload } from '@/components/ui/image-upload';
import { UserPriceTable } from '@/components/ui/user-price-table';
import { DisallowedGroupsTable } from '@/components/ui/disallowed-groups-table';
import { TimeGrid } from '@/components/ui/time-grid';
import { useProductForm } from '@/hooks/use-product-form';

interface AddProductPageProps {
  onSuccess?: () => void;
  className?: string;
}

export function AddProductPage({ onSuccess, className }: AddProductPageProps) {
  const {
    form,
    productGroups,
    userGroups,
    imagePreview,
    userPrices,
    disallowedGroups,
    timeSlots,
    loading,
    successMessage,
    onSubmit,
    handleImageSelect,
    handleImageRemove,
    setUserPrices,
    setDisallowedGroups,
    setTimeSlots,
    restoreFormState,
  } = useProductForm({
    onSuccess: () => {
      if (onSuccess) {
        onSuccess();
      }
    }
  });

  // 恢复表单状态
  useEffect(() => {
    restoreFormState();
  }, [restoreFormState]);

  return (
    <div className={`container mx-auto py-8 ${className}`}>
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <Package className="h-8 w-8 text-primary" />
          <h1 className="text-3xl font-bold text-gray-900">添加新产品</h1>
        </div>
        <p className="text-gray-600">创建新的产品并设置相关属性</p>
      </div>

      {/* 成功消息 */}
      {successMessage.show && (
        <Alert className="mb-6 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            {successMessage.message}
          </AlertDescription>
        </Alert>
      )}

      {/* 加载指示器 */}
      {loading.isLoading && (
        <Alert className="mb-6">
          <Loader2 className="h-4 w-4 animate-spin" />
          <AlertDescription>
            {loading.message || '处理中...'}
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={onSubmit} className="space-y-6">
          <ProductFormSections>
            {/* 基本信息部分 */}
            <GeneralSection>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <BasicInfoFields form={form} productGroups={productGroups} />
                  <div className="mt-6">
                    <PricingFields form={form} />
                  </div>
                </div>
                
                <div className="lg:col-span-1">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      产品图片
                    </label>
                    <ImageUpload
                      onImageSelect={handleImageSelect}
                      onImageRemove={handleImageRemove}
                      preview={imagePreview}
                      width={400}
                      height={400}
                    />
                  </div>
                </div>
              </div>
            </GeneralSection>

            {/* 用户价格部分 */}
            <PricingSection>
              <div className="space-y-4">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    用户组价格设置
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    为不同用户组设置特殊价格，未设置的用户组将使用默认价格
                  </p>
                </div>
                <UserPriceTable
                  userGroups={userGroups}
                  onPricesChange={setUserPrices}
                  initialPrices={userPrices}
                />
              </div>
            </PricingSection>

            {/* 限制部分 */}
            <RestrictionsSection>
              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    销售限制
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    设置产品的销售限制和访问控制
                  </p>
                </div>
                <RestrictionsFields form={form} />
                
                <div className="mt-8">
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    禁止用户组
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    选择不允许购买此产品的用户组
                  </p>
                  <DisallowedGroupsTable
                    userGroups={userGroups}
                    onDisallowedGroupsChange={setDisallowedGroups}
                    initialDisallowedGroups={disallowedGroups}
                  />
                </div>
              </div>
            </RestrictionsSection>

            {/* 可用性部分 */}
            <AvailabilitySection>
              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    时间范围限制
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    设置产品的可购买时间范围
                  </p>
                </div>
                
                <div className="flex items-center space-x-2 mb-4">
                  <input
                    type="checkbox"
                    id="enableTimeRange"
                    className="rounded border-gray-300"
                    {...form.register('timeRange')}
                  />
                  <label htmlFor="enableTimeRange" className="text-sm font-medium">
                    启用时间范围限制
                  </label>
                </div>

                {form.watch('timeRange') && (
                  <TimeGrid
                    onSelectionChange={setTimeSlots}
                    initialSelection={timeSlots}
                  />
                )}
              </div>
            </AvailabilitySection>

            {/* 库存部分 */}
            <StockSection>
              <div className="space-y-4">
                <div>
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    库存管理
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    配置产品的库存跟踪和警告设置
                  </p>
                </div>
                <StockFields form={form} />
              </div>
            </StockSection>
          </ProductFormSections>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button type="button" variant="outline" asChild>
              <Link href="/products">
                返回产品列表
              </Link>
            </Button>
            <Button 
              type="submit" 
              disabled={loading.isLoading}
              className="min-w-[120px]"
            >
              {loading.isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  添加中...
                </>
              ) : (
                '添加产品'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
