'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Camera, X, Upload } from 'lucide-react';

interface ImageUploadProps {
  onImageSelect: (file: File) => void;
  onImageRemove: () => void;
  preview?: string;
  className?: string;
  maxSize?: number; // in MB
  accept?: string;
  width?: number;
  height?: number;
}

export function ImageUpload({
  onImageSelect,
  onImageRemove,
  preview,
  className,
  maxSize = 5,
  accept = 'image/*',
  width = 400,
  height = 400,
}: ImageUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    if (file.size > maxSize * 1024 * 1024) {
      alert(`图片文件大小不能超过${maxSize}MB`);
      return;
    }

    onImageSelect(file);
  }, [maxSize, onImageSelect]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    
    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleClick = () => {
    if (!preview) {
      fileInputRef.current?.click();
    }
  };

  const handleRemove = (event: React.MouseEvent) => {
    event.stopPropagation();
    onImageRemove();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={cn('relative', className)}>
      <div
        className={cn(
          'border-2 border-dashed rounded-lg cursor-pointer transition-colors',
          'flex flex-col items-center justify-center',
          'bg-gray-50 hover:bg-gray-100',
          isDragOver && 'border-primary bg-primary/5',
          !isDragOver && 'border-gray-300 hover:border-gray-400',
          preview && 'border-solid border-gray-200'
        )}
        style={{ width, height }}
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {preview ? (
          <>
            <img
              src={preview}
              alt="预览"
              className="w-full h-full object-cover rounded-md"
            />
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 h-8 w-8"
              onClick={handleRemove}
            >
              <X className="h-4 w-4" />
            </Button>
          </>
        ) : (
          <div className="text-center p-6">
            <div className="mb-4">
              {isDragOver ? (
                <Upload className="h-12 w-12 text-primary mx-auto" />
              ) : (
                <Camera className="h-12 w-12 text-gray-400 mx-auto" />
              )}
            </div>
            <div className="text-sm text-gray-600">
              <div className="font-medium">{width}px × {height}px</div>
              <div className="mt-2">点击或拖拽上传图片</div>
              <div className="mt-1 text-xs text-gray-500">
                支持 JPG、PNG 格式，最大 {maxSize}MB
              </div>
            </div>
          </div>
        )}
      </div>
      
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleInputChange}
        className="hidden"
      />
    </div>
  );
}
