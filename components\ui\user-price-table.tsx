'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Trash2 } from 'lucide-react';
import { UserGroup } from '@/types/product';

interface UserPriceRow {
  userGroupId: number;
  userGroupName: string;
  enabled: boolean;
  price: string;
  purchaseOptions: string;
  pointsPrice: string;
}

interface UserPriceTableProps {
  userGroups: UserGroup[];
  onPricesChange: (prices: UserPriceRow[]) => void;
  initialPrices?: UserPriceRow[];
  className?: string;
}

export function UserPriceTable({ 
  userGroups, 
  onPricesChange, 
  initialPrices = [],
  className 
}: UserPriceTableProps) {
  const [userPrices, setUserPrices] = useState<UserPriceRow[]>(() => {
    // 初始化所有用户组的价格行
    return userGroups.map(group => {
      const existingPrice = initialPrices.find(p => p.userGroupId === group.id);
      return existingPrice || {
        userGroupId: group.id,
        userGroupName: group.name,
        enabled: false,
        price: '',
        purchaseOptions: '0',
        pointsPrice: ''
      };
    });
  });

  const updateUserPrice = useCallback((userGroupId: number, field: keyof UserPriceRow, value: any) => {
    const updatedPrices = userPrices.map(price => {
      if (price.userGroupId === userGroupId) {
        return { ...price, [field]: value };
      }
      return price;
    });
    
    setUserPrices(updatedPrices);
    onPricesChange(updatedPrices);
  }, [userPrices, onPricesChange]);

  const toggleEnabled = useCallback((userGroupId: number, enabled: boolean) => {
    updateUserPrice(userGroupId, 'enabled', enabled);
  }, [updateUserPrice]);

  const updatePrice = useCallback((userGroupId: number, price: string) => {
    updateUserPrice(userGroupId, 'price', price);
  }, [updateUserPrice]);

  const updatePurchaseOptions = useCallback((userGroupId: number, options: string) => {
    updateUserPrice(userGroupId, 'purchaseOptions', options);
  }, [updateUserPrice]);

  const updatePointsPrice = useCallback((userGroupId: number, pointsPrice: string) => {
    updateUserPrice(userGroupId, 'pointsPrice', pointsPrice);
  }, [updateUserPrice]);

  const removeUserPrice = useCallback((userGroupId: number) => {
    updateUserPrice(userGroupId, 'enabled', false);
    updateUserPrice(userGroupId, 'price', '');
    updateUserPrice(userGroupId, 'pointsPrice', '');
  }, [updateUserPrice]);

  return (
    <div className={className}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-16">启用</TableHead>
            <TableHead>用户组</TableHead>
            <TableHead className="w-32">价格 (RM)</TableHead>
            <TableHead className="w-32">购买选项</TableHead>
            <TableHead className="w-32">积分价格</TableHead>
            <TableHead className="w-16">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {userPrices.map((userPrice) => (
            <TableRow key={userPrice.userGroupId}>
              <TableCell>
                <Checkbox
                  checked={userPrice.enabled}
                  onCheckedChange={(checked) => 
                    toggleEnabled(userPrice.userGroupId, checked as boolean)
                  }
                />
              </TableCell>
              <TableCell className="font-medium">
                {userPrice.userGroupName}
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  value={userPrice.price}
                  onChange={(e) => updatePrice(userPrice.userGroupId, e.target.value)}
                  disabled={!userPrice.enabled}
                />
              </TableCell>
              <TableCell>
                <Select
                  value={userPrice.purchaseOptions}
                  onValueChange={(value) => updatePurchaseOptions(userPrice.userGroupId, value)}
                  disabled={!userPrice.enabled}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">And</SelectItem>
                    <SelectItem value="1">Or</SelectItem>
                  </SelectContent>
                </Select>
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  value={userPrice.pointsPrice}
                  onChange={(e) => updatePointsPrice(userPrice.userGroupId, e.target.value)}
                  disabled={!userPrice.enabled}
                />
              </TableCell>
              <TableCell>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeUserPrice(userPrice.userGroupId)}
                  disabled={!userPrice.enabled}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      <div className="mt-4 text-sm text-gray-600">
        已启用 {userPrices.filter(p => p.enabled).length} 个用户组的特殊价格
      </div>
    </div>
  );
}
