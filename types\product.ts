// 产品相关类型定义

export interface ProductGroup {
  id: number;
  name: string;
}

export interface UserGroup {
  id: number;
  name: string;
}

export interface TimeProduct {
  minutes: number;
  expiresAtLogout: boolean;
  expireAtDayTime: boolean;
  expireAtDayTimeMinute: number;
  expireAfterTime: boolean;
  expireAfterType: number;
  expiresAfter: number;
  expiresFrom: number;
  useOrder: number;
}

export interface Bundle {
  selfStock: boolean;
}

export interface Product {
  id?: number;
  productType: number;
  productGroupId: number;
  name: string;
  description: string;
  price: number;
  cost: number;
  disallowClientOrder: boolean;
  restrictGuestSale: boolean;
  restrictSale: boolean;
  purchaseOptions: number;
  points: number;
  pointsPrice: number;
  barcode: string;
  enableStock: boolean;
  disallowSaleIfOutOfStock: boolean;
  stockAlert: boolean;
  stockAlertThreshold: number;
  stockTargetDifferentProduct: boolean;
  stockTargetProductId: number;
  stockProductAmount: number;
  isDeleted: boolean;
  isService: boolean;
  displayOrder: number;
  timeProduct: TimeProduct;
  bundle: Bundle;
}

export interface ProductFormData {
  name: string;
  description: string;
  productGroupId: string;
  price: string;
  cost: string;
  points: string;
  pointsPrice: string;
  barcode: string;
  productType: string;
  displayOrder: string;
  purchaseOptions: string;
  enableStock: boolean;
  stockAlertThreshold: string;
  disallowSaleIfOutOfStock: boolean;
  stockAlert: boolean;
  disallowClientOrder: boolean;
  restrictGuestSale: boolean;
  restrictSale: boolean;
  timeRange: boolean;
}

export interface UserPrice {
  id?: number;
  userGroupId: number;
  price: number;
  pointsPrice: number;
  purchaseOptions: number;
  isEnabled: boolean;
}

export interface DisallowedUserGroup {
  id?: number;
  userGroupId: number;
  isDisallowed: boolean;
}

export interface TimeSlot {
  day: string;
  hour: number;
  selected: boolean;
}

export interface ApiResponse<T> {
  result?: T;
  success?: boolean;
  message?: string;
  errorCodeReadable?: string;
}

export interface PaginatedResponse<T> {
  result: {
    data: T[];
    totalCount: number;
  };
}

export interface ProductImage {
  id: number;
  image: string;
}

export interface ProductUserPrice {
  id: number;
  userGroupId: number;
  price: number;
  pointsPrice: number;
  purchaseOptions: number;
  isEnabled: boolean;
}

export interface ProductDisallowedUserGroup {
  id: number;
  userGroupId: number;
  isDisallowed: boolean;
}

// 表单验证错误类型
export interface FormErrors {
  [key: string]: string;
}

// 加载状态类型
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

// 成功消息类型
export interface SuccessMessage {
  show: boolean;
  message: string;
}
