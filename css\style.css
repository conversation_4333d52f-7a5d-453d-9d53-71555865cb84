/* 现代化设计系统 - Tailwind/Framer 风格 */
:root {
    /* 主色调系统 */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;

    /* 中性色系统 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* 功能色系统 */
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    --danger-50: #fef2f2;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;

    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    --info-50: #f0f9ff;
    --info-500: #06b6d4;
    --info-600: #0891b2;

    /* 语义化颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --bg-card: #ffffff;

    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-400);

    --border-color: var(--gray-200);
    --border-light: var(--gray-100);

    /* 现代化阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* 间距系统 */
    --space-1: 0.25rem;  /* 4px */
    --space-2: 0.5rem;   /* 8px */
    --space-3: 0.75rem;  /* 12px */
    --space-4: 1rem;     /* 16px */
    --space-5: 1.25rem;  /* 20px */
    --space-6: 1.5rem;   /* 24px */
    --space-8: 2rem;     /* 32px */
    --space-10: 2.5rem;  /* 40px */
    --space-12: 3rem;    /* 48px */

    /* 圆角系统 */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.375rem;  /* 6px */
    --radius-lg: 0.5rem;    /* 8px */
    --radius-xl: 0.75rem;   /* 12px */
    --radius-2xl: 1rem;     /* 16px */
    --radius-3xl: 1.5rem;   /* 24px */
    --radius-full: 9999px;

    /* 现代化过渡系统 */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: 300ms cubic-bezier(0.34, 1.56, 0.64, 1);

    /* 字体系统 */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* 兼容性变量 */
    --primary-color: var(--primary-500);
    --primary-hover: var(--primary-600);
    --secondary-color: var(--gray-500);
    --success-color: var(--success-500);
    --danger-color: var(--danger-500);
    --warning-color: var(--warning-500);
    --border-radius: var(--radius-lg);
    --border-radius-lg: var(--radius-xl);
    --border-radius-sm: var(--radius-sm);
    --transition: var(--transition-normal);
    --transition-fast: var(--transition-fast);
}

/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 50%, var(--primary-50) 100%);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    min-height: 100vh;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h2, h3 {
    color: #343a40;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    display: inline-block;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}

/* 现代化按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
    white-space: nowrap;
    width: auto;
    margin: 0;
    position: relative;
    overflow: hidden;
    transform: translateY(0);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border: 1px solid transparent;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--gray-300);
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    border-color: var(--text-secondary);
}

.btn-icon {
    font-size: 1rem;
}

/* 传统按钮样式保持兼容性 */
button:not(.btn) {
    width: 100%;
    background-color: #007bff;
    color: white;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

button:not(.btn):hover {
    background-color: #0056b3;
}

/* 状态样式 */
.error {
    color: var(--danger-color);
    margin-top: 10px;
    font-weight: bold;
}

.no-results {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.error-message {
    text-align: center;
    padding: 2rem;
    color: var(--danger-color);
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* 加载指示器 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    color: var(--text-secondary);
    font-size: 1rem;
}

/* 现代化加载动画 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
}

/* 现代化动画系统 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
        border-top-color: var(--primary-500);
    }
    50% {
        border-top-color: var(--primary-600);
    }
    100% {
        transform: rotate(360deg);
        border-top-color: var(--primary-500);
    }
}

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滑入动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse-red {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    }
    50% {
        box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        height: 100vh;
        height: 100dvh; /* 动态视口高度，适配移动端 */
    }

    .header-content {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-right {
        justify-content: center;
    }

    .main-content {
        padding: 1rem;
        overflow: hidden;
    }

    .app-title {
        font-size: 1.3rem;
        justify-content: center;
    }

    .controls-row {
        flex-direction: column;
        gap: 0.75rem;
    }

    .group-selector-container {
        min-width: auto;
    }

    .search-input-wrapper {
        max-width: none;
    }

    .stock-filter {
        justify-content: center;
    }

    .stats-row {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .product-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
        padding: 1rem;
    }

    .product-info {
        text-align: center;
    }

    .product-stock {
        margin-left: 0;
        text-align: center;
    }

    .product-actions {
        opacity: 1;
        visibility: visible;
        margin-left: 0;
        flex-wrap: wrap;
        justify-content: center;
    }

    .stock-input {
        width: 80px;
    }

    .edit-btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0.5rem;
    }

    .main-content {
        padding: 0.5rem;
    }

    .app-title {
        font-size: 1.1rem;
    }

    .product-item {
        padding: 1rem;
    }

    .group-item {
        padding: 1rem;
    }
}

/* 产品页面样式 */
#searchInput {
    margin-bottom: 15px;
    padding: 10px;
    width: 100%;
    max-width: 400px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 16px;
}

/* 应用容器 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 现代化顶部导航栏 */
.app-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition-fast);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-4) var(--space-8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 4rem;
}

.header-left {
    display: flex;
    align-items: center;
}

.app-title {
    margin: 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    background: linear-gradient(135deg, var(--primary-600), var(--primary-500));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-icon {
    font-size: 1.8rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 现代化顶部控制区域 */
.top-controls {
    background: var(--bg-card);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
    border: 1px solid var(--border-light);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.controls-row {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
    flex-wrap: wrap;
}

.controls-row:not(:last-child) {
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-light);
}

/* 产品组选择器 */
.group-selector-container {
    position: relative;
    min-width: 150px;
}

.group-selector {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    font-size: 0.9rem;
    color: var(--text-primary);
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.group-selector:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.dropdown-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
    font-size: 0.8rem;
}

/* 搜索输入框 */
.search-input-wrapper {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-sm);
    background: var(--bg-secondary);
    transition: var(--transition-fast);
    font-weight: var(--font-weight-normal);
    color: var(--text-primary);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    background: var(--bg-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.search-loading {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
}

/* 库存筛选 */
.stock-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.stock-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.stock-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    cursor: pointer;
    margin: 0;
}

/* 排序选择器 */
.sort-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background: var(--bg-primary);
    font-size: 0.9rem;
    color: var(--text-primary);
    cursor: pointer;
    min-width: 150px;
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

/* 统计信息行 */
.stats-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.item-count {
    font-weight: 500;
}

.unit-count {
    color: var(--text-muted);
}

/* 网格布局 */
.group-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 关闭按钮样式 */
.close-button {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
    color: white;
    padding: 1rem 1.5rem;
    text-align: center;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    grid-column: 1 / -1; /* 占满整行 */
    margin-bottom: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.close-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    border-color: rgba(255, 255, 255, 0.3);
}

.close-icon {
    font-size: 1.3rem;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-text {
    font-size: 1rem;
    letter-spacing: 0.5px;
}

/* 现代化产品列表 */
.product-list {
    background: var(--bg-card);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    overflow-y: auto;
    flex: 1;
    min-height: 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.product-list:empty {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 自定义滚动条样式 */
.product-list::-webkit-scrollbar {
    width: 8px;
}

.product-list::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.product-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
    transition: var(--transition);
}

.product-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Firefox滚动条样式 */
.product-list {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--bg-secondary);
}

/* 现代化产品项 */
.product-item {
    display: flex;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-light);
    transition: var(--transition-fast);
    cursor: pointer;
    background: transparent;
    flex-wrap: wrap;
    gap: var(--space-4);
    position: relative;
}

.product-item:last-child {
    border-bottom: none;
}

.product-item:hover {
    background: linear-gradient(135deg, var(--bg-secondary), var(--primary-50));
    transform: translateY(-1px);
}

.product-item:hover .product-actions {
    opacity: 1;
    visibility: visible;
}

.product-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    opacity: 0;
    transition: var(--transition-fast);
}

.product-item:hover::before {
    opacity: 1;
}

/* 产品图标/缩略图 */
.product-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-sm);
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    font-size: 1.2rem;
}

.product-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
}

/* 产品信息 */
.product-info {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
}

.product-details {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

.product-price {
    color: var(--text-primary);
    font-weight: 500;
}

/* 库存信息 */
.product-stock {
    text-align: right;
    flex-shrink: 0;
    margin-left: 1rem;
}

.stock-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    display: block;
    line-height: 1.2;
}

.stock-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 库存状态样式 */
.stock-number.stock-zero {
    color: var(--danger-color);
}

.stock-number.stock-disabled {
    color: var(--text-muted);
    font-style: italic;
}

.stock-number.stock-low {
    color: var(--warning-color);
}

/* 产品操作区域 */
.product-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    flex-shrink: 0;
}

.stock-input {
    width: 60px;
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    text-align: center;
}

.stock-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.add-stock-btn {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    color: white;
}

.add-stock-btn:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    transform: scale(1.05) translateY(-1px);
    box-shadow: var(--shadow-md);
}

.remove-stock-btn {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
    color: white;
}

.remove-stock-btn:hover {
    background: linear-gradient(135deg, var(--danger-600), var(--danger-700));
    transform: scale(1.05) translateY(-1px);
    box-shadow: var(--shadow-md);
}

.edit-btn {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
    box-shadow: var(--shadow-xs);
}

.quick-edit-btn:hover {
    background: linear-gradient(135deg, var(--info-500), var(--info-600));
    color: white;
    border-color: var(--info-500);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.advanced-edit-btn:hover {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border-color: var(--primary-500);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 产品编辑信息区域 */
.product-edit-info {
    width: 100%;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 1rem;
    box-shadow: var(--shadow-sm);
}

.product-edit-info div {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.product-edit-info strong {
    color: var(--text-primary);
    font-weight: 600;
}

.edit-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.quick-edit-btn {
    background: var(--info-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 0.8rem;
    transition: var(--transition);
}

.quick-edit-btn:hover {
    background: #138496;
    transform: translateY(-1px);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.empty-state-subtext {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.group-button {
    padding: 15px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    font-size: 14px;
}

.group-button:hover {
    background-color: #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-button[data-expanded="true"],
.group-button.expanded {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.product-list {
    margin-top: 20px;
}

/* 现代化产品卡片 - 垂直列表样式 */
.product-item {
    background: var(--bg-primary);
    padding: 1.5rem;
    margin-bottom: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.product-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.05), transparent);
    transition: left 0.5s;
}

.product-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.product-item:hover::before {
    left: 100%;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.stock-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.stock-input {
    width: 80px;
    padding: 5px;
}

.add-stock-btn, .remove-stock-btn, .set-stock-btn {
    padding: 5px 10px;
    width: auto;
    font-size: 12px;
}

.add-stock-btn {
    background-color: #28a745; /* 绿色 */
}

.add-stock-btn:hover {
    background-color: #218838;
}

.remove-stock-btn {
    background-color: #dc3545; /* 红色 */
}

.remove-stock-btn:hover {
    background-color: #c82333;
}

.set-stock-btn {
    background-color: #ffc107; /* 黄色 */
    color: #212529;
}

.set-stock-btn:hover {
    background-color: #e0a800;
}

.product-info {
    background-color: #f8f9fa;
    padding: 15px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

.product-info div {
    margin-bottom: 5px;
}

#logoutBtn {
    width: auto;
    background-color: #dc3545;
    padding: 10px 20px;
    margin-top: 20px;
}

#logoutBtn:hover {
    background-color: #c82333;
}

.loading {
    text-align: center;
    font-size: 18px;
    color: #6c757d;
    margin: 20px 0;
}

.edit-btn, .edit-form button {
    padding: 5px 10px;
    font-size: 12px;
    margin-left: 10px;
    width: auto;
}

.edit-form {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 10px;
    align-items: center;
}

.edit-form label {
    font-weight: bold;
    text-align: right;
}

.edit-form input {
    margin: 0;
    padding: 8px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

.edit-form button {
    grid-column: 1 / -1; /* 让按钮横跨两列 */
}

/* 库存显示样式 */
.stock-display {
    font-weight: 600;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
    min-width: 80px;
    text-align: center;
    transition: all 0.3s ease;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.stock-normal {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    color: #2d5a2d;
    border: 1px solid #c3e6c3;
    box-shadow: 0 2px 4px rgba(45, 90, 45, 0.1);
}

.stock-zero {
    background: linear-gradient(135deg, #ffe6e6 0%, #fff0f0 100%);
    color: #d32f2f;
    border: 1px solid #ffcdd2;
    box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    animation: pulse-red 2s infinite;
}

.stock-disabled {
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    color: #666;
    border: 1px solid #ccc;
    box-shadow: 0 2px 4px rgba(102, 102, 102, 0.1);
    font-style: italic;
}

@keyframes pulse-red {
    0%, 100% {
        box-shadow: 0 2px 4px rgba(211, 47, 47, 0.15);
    }
    50% {
        box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
    }
}

/* 库存控制区域样式优化 */
.stock-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.stock-control .stock-input {
    width: 60px;
    padding: 4px 6px;
    font-size: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.stock-control button {
    padding: 4px 8px;
    font-size: 11px;
    margin: 0;
    width: auto;
    min-width: 50px;
}