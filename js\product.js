// 全局变量
let allGroups = [];
let allProducts = [];
const productCache = {}; // 缓存产品列表
let searchTimeout = null; // 搜索防抖定时器
let currentSearchController = null; // 当前搜索请求控制器
let isSearching = false; // 搜索状态标识
let currentExpandedGroup = null; // 当前展开的组

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    const token = localStorage.getItem('token');
    if (!token) {
        alert('请先登入');
        window.location.href = 'login.html';
        return;
    }

    document.getElementById('logoutBtn').onclick = handleLogout;
    document.getElementById('searchInput').addEventListener('input', handleSearchWithDelay);
    document.getElementById('groupSelector').addEventListener('change', handleGroupSelection);
    document.getElementById('inStockFilter').addEventListener('change', handleFilterChange);
    document.getElementById('applySortBtn').addEventListener('click', handleSort);

    await loadInitialData();
});

// 登出处理
function handleLogout() {
    localStorage.removeItem('token');
    window.location.href = 'login.html';
}

// 加载初始数据
async function loadInitialData() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    loadingIndicator.style.display = 'block';

    try {
        const [groupsData, productsData] = await Promise.all([
            fetchProductGroups(),
            fetchAllProducts()
        ]);

        allGroups = groupsData.result?.data || groupsData.result || [];
        allProducts = productsData.result?.data || [];

        populateGroupSelector();
        updateStats();
        showEmptyState();
    } catch (error) {
        console.error('加载初始数据失败:', error);
        alert('加载数据失败，请稍后重试。');
    } finally {
        loadingIndicator.style.display = 'none';
    }
}

// 填充产品组选择器
function populateGroupSelector() {
    const selector = document.getElementById('groupSelector');
    selector.innerHTML = '<option value="">产品组</option>';

    allGroups.forEach(group => {
        const option = document.createElement('option');
        option.value = group.id;
        option.textContent = group.name;
        selector.appendChild(option);
    });
}

// 这个函数已被新的产品组选择器替代，保留空函数以避免错误
function renderGroups(groups) {
    // 新设计使用下拉选择器，不再需要渲染组按钮
}

// 这个函数已被新的handleGroupSelection替代
async function toggleProducts(groupId, btnElement) {
    // 新设计使用下拉选择器，不再需要切换逻辑
}

// 处理产品组选择
async function handleGroupSelection() {
    const selector = document.getElementById('groupSelector');
    const selectedGroupId = selector.value;

    if (!selectedGroupId) {
        showEmptyState();
        updateStats();
        return;
    }

    currentExpandedGroup = selectedGroupId;

    let products = productCache[selectedGroupId];
    if (!products) {
        products = allProducts.filter(p => p.productGroupId == selectedGroupId && !p.isDeleted);
        productCache[selectedGroupId] = products;
    }

    await renderProducts(products);
    updateStats(products);
}

// 处理筛选变化
async function handleFilterChange() {
    const selectedGroupId = document.getElementById('groupSelector').value;
    if (!selectedGroupId) return;

    await handleGroupSelection();
}

// 显示空状态
function showEmptyState() {
    const productList = document.getElementById('productList');
    productList.innerHTML = `
        <div class="empty-state">
            <div class="empty-state-icon">📦</div>
            <div class="empty-state-text">选择产品组查看产品</div>
            <div class="empty-state-subtext">从上方下拉菜单中选择一个产品组</div>
        </div>
    `;
}

// 更新统计信息
function updateStats(products = []) {
    const itemCount = document.getElementById('itemCount');
    const unitCount = document.getElementById('unitCount');

    itemCount.textContent = `${products.length} items`;

    // 计算总库存（这里简化处理，实际可能需要异步获取库存）
    const totalUnits = products.length * 10; // 简化计算
    unitCount.textContent = `${totalUnits} units`;
}

// 显示快速编辑（适配新按钮）
function showQuickEdit(product) {
    // 创建一个临时的容器来显示快速编辑表单
    const tempContainer = document.createElement('div');
    showQuickEditForm(tempContainer, product);

    // 创建模态框显示快速编辑
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        padding: 2rem;
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    `;

    modalContent.appendChild(tempContainer);

    // 添加关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.textContent = '关闭';
    closeBtn.style.cssText = `
        margin-top: 1rem;
        padding: 0.5rem 1rem;
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    `;
    closeBtn.onclick = () => document.body.removeChild(modal);
    modalContent.appendChild(closeBtn);

    modal.appendChild(modalContent);
    modal.onclick = (e) => {
        if (e.target === modal) document.body.removeChild(modal);
    };

    document.body.appendChild(modal);
}

// 显示高级编辑（适配新按钮）
function showAdvancedEdit(product) {
    showAdvancedEditModal(product);
}

// 显示快速修改模态框
function showQuickEditModal(product) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        padding: 2rem;
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    `;

    modalContent.innerHTML = `
        <h3>快速修改产品</h3>
        <div class="edit-form">
            <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">产品名称:</label>
                <input type="text" id="modal-edit-name-${product.id}" value="${product.name}"
                       style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">价格 (RM):</label>
                <input type="number" id="modal-edit-price-${product.id}" value="${product.price || 0}" step="0.01"
                       style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">成本 (RM):</label>
                <input type="number" id="modal-edit-cost-${product.id}" value="${product.cost || 0}" step="0.01"
                       style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">条码:</label>
                <input type="text" id="modal-edit-barcode-${product.id}" value="${product.barcode || ''}"
                       style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
            </div>
        </div>
    `;

    // 添加按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
        display: flex;
        gap: 1rem;
        margin-top: 1.5rem;
        justify-content: flex-end;
    `;

    // 保存按钮
    const saveBtn = document.createElement('button');
    saveBtn.textContent = '保存';
    saveBtn.style.cssText = `
        padding: 0.5rem 1rem;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 600;
    `;
    saveBtn.onclick = async () => {
        try {
            await saveModalProductChanges(product.id);
            document.body.removeChild(modal);
            // 重新加载产品列表以显示更新
            await loadProducts();
        } catch (error) {
            alert('保存失败: ' + error.message);
        }
    };

    // 取消按钮
    const cancelBtn = document.createElement('button');
    cancelBtn.textContent = '取消';
    cancelBtn.style.cssText = `
        padding: 0.5rem 1rem;
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    `;
    cancelBtn.onclick = () => document.body.removeChild(modal);

    buttonContainer.appendChild(saveBtn);
    buttonContainer.appendChild(cancelBtn);
    modalContent.appendChild(buttonContainer);

    modal.appendChild(modalContent);
    modal.onclick = (e) => {
        if (e.target === modal) document.body.removeChild(modal);
    };

    document.body.appendChild(modal);
}

// 保存模态框中的产品更改
async function saveModalProductChanges(productId) {
    const product = allProducts.find(p => p.id == productId);
    const updatedProduct = {
        ...product,
        name: document.getElementById(`modal-edit-name-${productId}`).value,
        price: parseFloat(document.getElementById(`modal-edit-price-${productId}`).value),
        cost: parseFloat(document.getElementById(`modal-edit-cost-${productId}`).value),
        barcode: document.getElementById(`modal-edit-barcode-${productId}`).value,
    };

    await updateProduct(updatedProduct);
    // 更新本地缓存
    const index = allProducts.findIndex(p => p.id == productId);
    allProducts[index] = updatedProduct;
}

// 渲染产品列表
async function renderProducts(products, abortSignal = null) {
    const container = document.getElementById('productList');
    container.innerHTML = '';

    if (products.length === 0) {
        showEmptyState();
        return;
    }

    // 应用库存筛选
    const inStockFilter = document.getElementById('inStockFilter').checked;
    let filteredProducts = products;

    if (inStockFilter) {
        // 这里简化处理，实际应该检查真实库存
        filteredProducts = products.filter(p => true); // 暂时显示所有产品
    }

    for (const product of filteredProducts) {
        // 检查是否被取消
        if (abortSignal && abortSignal.aborted) {
            throw new DOMException('Aborted', 'AbortError');
        }

        const item = document.createElement('div');
        item.className = 'product-item';
        item.dataset.productId = product.id;

        const stock = await fetchProductStock(product.id);

        // 检查库存是否被禁用或不可用
        const isStockDisabled = stock === '库存已禁用' || stock === '不可用';

        // 根据库存数量设置样式
        let stockClass = 'stock-normal';
        if (isStockDisabled) {
            stockClass = 'stock-disabled';
        } else if (stock === 0) {
            stockClass = 'stock-zero';
        }

        // 确定库存样式类
        if (isStockDisabled) {
            stockClass = 'stock-disabled';
        } else if (stock === 0) {
            stockClass = 'stock-zero';
        } else if (stock < 10) {
            stockClass = 'stock-low';
        }

        // 格式化价格显示
        const priceText = product.price ? `RM${product.price.toFixed(2)}` : 'RM0.00';
        const costText = product.cost ? `RM${product.cost.toFixed(2)}` : 'RM0.00';

        // 获取产品组名称
        const productGroup = allGroups.find(g => g.id === product.productGroupId);
        const groupName = productGroup ? productGroup.name : 'NANO';

        // 创建产品项HTML
        if (isStockDisabled) {
            item.innerHTML = `
                <div class="product-thumbnail">
                    📦
                </div>
                <div class="product-info">
                    <div class="product-name">${product.name}</div>
                    <div class="product-details">
                        <span class="product-price">${priceText}</span> / ${costText} /
                        ${product.barcode || 'NANO'} / ${groupName}
                    </div>
                </div>
                <div class="product-stock">
                    <span class="stock-number ${stockClass}">${stock}</span>
                    <span class="stock-label">units</span>
                </div>
            `;
        } else {
            item.innerHTML = `
                <div class="product-thumbnail">
                    📦
                </div>
                <div class="product-info">
                    <div class="product-name">${product.name}</div>
                    <div class="product-details">
                        <span class="product-price">${priceText}</span> / ${costText} /
                        ${product.barcode || 'NANO'} / ${groupName}
                    </div>
                </div>
                <div class="product-stock">
                    <span class="stock-number ${stockClass}">${stock}</span>
                    <span class="stock-label">units</span>
                </div>
                <div class="product-actions">
                    <input type="number" class="stock-input" placeholder="数量" min="1">
                    <button class="stock-btn add-stock-btn">+</button>
                    <button class="stock-btn remove-stock-btn">-</button>
                    <button class="edit-btn quick-edit-btn">快速修改</button>
                    <button class="edit-btn advanced-edit-btn">高级编辑</button>
                </div>
            `;
        }

        // 添加库存操作事件监听器（仅对启用库存的产品）
        if (!isStockDisabled) {
            const stockInput = item.querySelector('.stock-input');
            const stockNumber = item.querySelector('.stock-number');
            const addBtn = item.querySelector('.add-stock-btn');
            const removeBtn = item.querySelector('.remove-stock-btn');
            const quickEditBtn = item.querySelector('.quick-edit-btn');
            const advancedEditBtn = item.querySelector('.advanced-edit-btn');

            // 增加库存
            addBtn.onclick = async (e) => {
                e.stopPropagation();
                const amount = parseInt(stockInput.value) || 1;
                try {
                    await updateStock(product.id, amount, 0); // type 0 = Add
                    const currentStock = parseInt(stockNumber.textContent) || 0;
                    const newStock = currentStock + amount;
                    stockNumber.textContent = newStock;
                    updateStockStyle(stockNumber, newStock);
                    stockInput.value = '';
                } catch (error) {
                    alert('增加库存失败: ' + error.message);
                }
            };

            // 减少库存
            removeBtn.onclick = async (e) => {
                e.stopPropagation();
                const amount = parseInt(stockInput.value) || 1;
                try {
                    await updateStock(product.id, amount, 1); // type 1 = Remove
                    const currentStock = parseInt(stockNumber.textContent) || 0;
                    const newStock = Math.max(0, currentStock - amount);
                    stockNumber.textContent = newStock;
                    updateStockStyle(stockNumber, newStock);
                    stockInput.value = '';
                } catch (error) {
                    alert('减少库存失败: ' + error.message);
                }
            };

            // 快速修改
            quickEditBtn.onclick = (e) => {
                e.stopPropagation();
                showQuickEditModal(product);
            };

            // 高级编辑
            advancedEditBtn.onclick = (e) => {
                e.stopPropagation();
                showAdvancedEdit(product);
            };
        }

        // 移除产品项的点击事件，只保留按钮操作
        // item.onclick = null;

        // 添加淡入动画
        item.style.opacity = '0';
        item.style.transform = 'translateY(10px)';
        container.appendChild(item);

        // 触发动画
        requestAnimationFrame(() => {
            item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        });
    }
}

// 更新库存
async function updateStock(productId, amount, type) {
    try {
        await updateProductStock(productId, amount, type);
    } catch (error) {
        alert('更新库存失败: ' + error.message);
    }
}

// 显示产品详情
function showProductDetails(product, stock) {
    // 创建产品详情模态框
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        padding: 2rem;
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    `;

    modalContent.innerHTML = `
        <h3>产品详情</h3>
        <div style="margin-bottom: 1rem;">
            <strong>名称:</strong> ${product.name}
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>价格:</strong> RM ${product.price?.toFixed(2) || '0.00'}
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>成本:</strong> RM ${product.cost?.toFixed(2) || '0.00'}
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>库存:</strong> ${stock}
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>条码:</strong> ${product.barcode || '无'}
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>产品ID:</strong> ${product.id}
        </div>
    `;

    // 添加关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.textContent = '关闭';
    closeBtn.style.cssText = `
        margin-top: 1rem;
        padding: 0.5rem 1rem;
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    `;
    closeBtn.onclick = () => document.body.removeChild(modal);
    modalContent.appendChild(closeBtn);

    modal.appendChild(modalContent);
    modal.onclick = (e) => {
        if (e.target === modal) document.body.removeChild(modal);
    };

    document.body.appendChild(modal);
}

// 切换产品详情（快速修改）
async function toggleProductDetails(product, itemElement) {
    const existingEditInfo = itemElement.querySelector('.product-edit-info');
    if (existingEditInfo) {
        existingEditInfo.remove();
        return;
    }

    const stock = await fetchProductStock(product.id);
    const infoContainer = document.createElement('div');
    infoContainer.className = 'product-edit-info';

    // 设置产品信息
    infoContainer.innerHTML = `
        <div><strong>${product.name}</strong></div>
        <div>价格：RM ${product.price?.toFixed(2) || 'N/A'}</div>
        <div>成本：RM ${product.cost?.toFixed(2) || 'N/A'}</div>
        <div>库存：${stock}</div>
        <div>条码：${product.barcode || '无'}</div>
    `;

    // 创建并添加“修改”按钮
    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'edit-buttons';
    buttonContainer.style.marginTop = '10px';

    // 创建"快速修改"按钮
    const quickEditButton = document.createElement('button');
    quickEditButton.className = 'quick-edit-btn';
    quickEditButton.textContent = '快速修改';
    quickEditButton.style.marginRight = '10px';
    quickEditButton.addEventListener('click', (event) => {
        event.stopPropagation();
        showQuickEditForm(infoContainer, product);
    });

    // 创建"高级编辑"按钮
    const advancedEditButton = document.createElement('button');
    advancedEditButton.className = 'advanced-edit-btn';
    advancedEditButton.textContent = '高级编辑';
    advancedEditButton.addEventListener('click', (event) => {
        event.stopPropagation();
        showAdvancedEditModal(product);
    });

    buttonContainer.appendChild(quickEditButton);
    buttonContainer.appendChild(advancedEditButton);
    infoContainer.appendChild(buttonContainer);
    itemElement.appendChild(infoContainer);
}

// 显示快速编辑表单
function showQuickEditForm(infoContainer, product) {
    infoContainer.innerHTML = `
        <div class="edit-form">
            <label>Name:</label>
            <input type="text" id="edit-name-${product.id}" value="${product.name}">
            <label>Price:</label>
            <input type="number" id="edit-price-${product.id}" value="${product.price || 0}">
            <label>Cost:</label>
            <input type="number" id="edit-cost-${product.id}" value="${product.cost || 0}">
            <label>Barcode:</label>
            <input type="text" id="edit-barcode-${product.id}" value="${product.barcode || ''}">
            <button id="save-btn-${product.id}">保存</button>
            <button id="cancel-btn-${product.id}">取消</button>
        </div>
    `;

    // 阻止整个表单的点击事件冒泡
    infoContainer.querySelector('.edit-form').addEventListener('click', (event) => {
        event.stopPropagation();
    });

    // 为按钮添加事件监听器
    document.getElementById(`save-btn-${product.id}`).addEventListener('click', () => saveProductChanges(product.id));
    document.getElementById(`cancel-btn-${product.id}`).addEventListener('click', () => cancelEdit(infoContainer));
}

// 取消编辑
function cancelEdit(infoContainer) {
    const productId = infoContainer.parentElement.dataset.productId;
    const product = allProducts.find(p => p.id == productId);
    toggleProductDetails(product, infoContainer.parentElement);
}

// 保存产品更改
async function saveProductChanges(productId) {
    const product = allProducts.find(p => p.id == productId);
    const updatedProduct = {
        ...product,
        name: document.getElementById(`edit-name-${productId}`).value,
        price: parseFloat(document.getElementById(`edit-price-${productId}`).value),
        cost: parseFloat(document.getElementById(`edit-cost-${productId}`).value),
        barcode: document.getElementById(`edit-barcode-${productId}`).value,
    };

    try {
        await updateProduct(updatedProduct);
        // 更新本地缓存
        const index = allProducts.findIndex(p => p.id == productId);
        allProducts[index] = updatedProduct;
        // 重新渲染产品详情
        const itemElement = document.querySelector(`[data-product-id='${productId}']`);
        const infoContainer = itemElement.querySelector('.product-edit-info');
        if (infoContainer) {
            infoContainer.remove(); // 先移除旧的详情
        }
        toggleProductDetails(updatedProduct, itemElement); // 重新创建并显示新的详情
    } catch (error) {
        alert('更新产品失败: ' + error.message);
    }
}

// 带防抖的搜索处理
function handleSearchWithDelay() {
    // 取消之前的搜索请求
    if (currentSearchController) {
        currentSearchController.abort();
        currentSearchController = null;
    }

    // 清除之前的定时器
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    // 设置新的定时器，300ms后执行搜索
    searchTimeout = setTimeout(() => {
        handleSearch();
    }, 300);
}

// 处理搜索
async function handleSearch() {
    // 防止重复搜索
    if (isSearching) {
        return;
    }

    const keyword = document.getElementById('searchInput').value.toLowerCase().trim();

    if (!keyword) {
        // 清空搜索时，如果有选中的组则显示该组的产品，否则显示空状态
        const selectedGroupId = document.getElementById('groupSelector').value;
        if (selectedGroupId) {
            await handleGroupSelection();
        } else {
            showEmptyState();
            updateStats();
        }
        return;
    }

    // 设置搜索状态
    isSearching = true;
    currentSearchController = new AbortController();

    // 显示搜索指示器
    showSearchIndicator();

    try {
        // 1. 过滤产品 - 根据产品名称和条码匹配，排除已删除产品
        const matchedProducts = allProducts.filter(product => {
            const productName = product.name.toLowerCase();
            const barcode = (product.barcode || '').toLowerCase();

            // 产品名称包含关键词或条码以关键词开头，且产品未被删除
            return !product.isDeleted && (productName.includes(keyword) || barcode.startsWith(keyword));
        });

        // 2. 分别处理产品组匹配和产品匹配
        const groupNameMatchedGroups = allGroups.filter(group => {
            const groupName = group.name.toLowerCase();
            return groupName.includes(keyword);
        });

        const productMatchedGroupIds = new Set(matchedProducts.map(p => p.productGroupId));
        const productMatchedGroups = allGroups.filter(group =>
            productMatchedGroupIds.has(group.id) && !groupNameMatchedGroups.some(g => g.id === group.id)
        );

        // 3. 合并所有匹配的产品组
        const allMatchedGroups = [...groupNameMatchedGroups, ...productMatchedGroups];

        // 4. 确定要显示的产品
        let finalProducts = [];

        // 对于产品组名称匹配的组，显示该组的所有产品（排除已删除产品）
        for (const group of groupNameMatchedGroups) {
            const groupProducts = allProducts.filter(p => p.productGroupId === group.id && !p.isDeleted);
            finalProducts.push(...groupProducts);
        }

        // 对于因包含匹配产品而被选中的组，只显示匹配的产品
        for (const group of productMatchedGroups) {
            const groupMatchedProducts = matchedProducts.filter(p => p.productGroupId === group.id);
            finalProducts.push(...groupMatchedProducts);
        }

        // 检查是否被取消
        if (currentSearchController.signal.aborted) {
            return;
        }

        // 5. 渲染搜索结果
        if (finalProducts.length > 0) {
            await renderProducts(finalProducts, currentSearchController.signal);
            updateStats(finalProducts);
        } else {
            document.getElementById('productList').innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">🔍</div>
                    <div class="empty-state-text">未找到匹配的产品</div>
                    <div class="empty-state-subtext">尝试使用不同的关键词搜索</div>
                </div>
            `;
            updateStats([]);
        }
    } catch (error) {
        if (error.name !== 'AbortError') {
            console.error('搜索出错:', error);
            document.getElementById('productList').innerHTML = '<div class="error-message">搜索出错，请重试</div>';
        }
    } finally {
        // 清理搜索状态
        isSearching = false;
        currentSearchController = null;
        hideSearchIndicator();
    }
}

// 显示搜索指示器
function showSearchIndicator() {
    const searchInput = document.getElementById('searchInput');
    searchInput.style.background = 'linear-gradient(90deg, #f0f8ff, #e6f3ff)';
    searchInput.style.borderColor = '#4a90e2';

    // 添加搜索图标动画
    const existingIcon = document.querySelector('.search-icon');
    if (existingIcon) {
        existingIcon.style.animation = 'spin 1s linear infinite';
    }
}

// 隐藏搜索指示器
function hideSearchIndicator() {
    const searchInput = document.getElementById('searchInput');
    searchInput.style.background = '';
    searchInput.style.borderColor = '';

    // 停止搜索图标动画
    const existingIcon = document.querySelector('.search-icon');
    if (existingIcon) {
        existingIcon.style.animation = '';
    }
}

// 排序处理
function handleSort() {
    const sortBy = document.getElementById('sortBy').value;
    const productList = document.getElementById('productList');

    if (!sortBy) {
        return;
    }

    // 获取所有产品元素
    const productElements = Array.from(document.querySelectorAll('.product-item'));

    if (productElements.length === 0) {
        return;
    }

    // 显示加载状态
    const sortBtn = document.getElementById('applySortBtn');
    const originalText = sortBtn.textContent;
    sortBtn.textContent = '排序中...';
    sortBtn.disabled = true;

    try {
        // 为每个元素提取排序数据
        const elementsWithData = productElements.map(element => {
            const productId = parseInt(element.dataset.productId);
            const nameElement = element.querySelector('.product-name');
            const stockElement = element.querySelector('.stock-number');

            const name = nameElement ? nameElement.textContent : '';
            const stockText = stockElement ? stockElement.textContent : '0';
            const stock = parseInt(stockText) || 0;

            // 从allProducts中获取价格信息
            const productData = allProducts.find(p => p.id === productId);
            const price = productData ? (parseFloat(productData.price) || 0) : 0;

            return {
                element,
                productId,
                name,
                stock,
                price
            };
        });

        console.log('提取的数据示例:', elementsWithData.slice(0, 3).map(item => ({
            id: item.productId,
            name: item.name,
            stock: item.stock,
            price: item.price
        })));

        // 排序
        elementsWithData.sort((a, b) => {
            switch (sortBy) {
                case 'name_asc':
                    return a.name.localeCompare(b.name, 'zh-CN', { numeric: true });
                case 'name_desc':
                    return b.name.localeCompare(a.name, 'zh-CN', { numeric: true });
                case 'stock_desc':
                    return b.stock - a.stock;
                case 'stock_asc':
                    return a.stock - b.stock;
                case 'price_desc':
                    return b.price - a.price;
                case 'price_asc':
                    return a.price - b.price;
                case 'id_desc':
                    return b.productId - a.productId;
                case 'id_asc':
                    return a.productId - b.productId;
                default:
                    return 0;
            }
        });

        console.log('排序后的数据示例:', elementsWithData.slice(0, 3).map(item => ({
            id: item.productId,
            name: item.name,
            stock: item.stock,
            price: item.price
        })));

        // 重新排列DOM元素
        productList.innerHTML = '';
        elementsWithData.forEach(item => {
            productList.appendChild(item.element);
        });

    } catch (error) {
        console.error('排序失败:', error);
    } finally {
        // 恢复按钮状态
        sortBtn.textContent = originalText;
        sortBtn.disabled = false;
    }
}

// 更新库存样式
function updateStockStyle(stockElement, stockValue) {
    // 移除现有的样式类
    stockElement.classList.remove('stock-zero', 'stock-normal');

    // 根据库存值添加相应的样式类
    if (stockValue === 0) {
        stockElement.classList.add('stock-zero');
    } else {
        stockElement.classList.add('stock-normal');
    }
}

// 显示高级编辑模态框
async function showAdvancedEditModal(product) {
    // 创建模态框背景
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';
    modalOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    `;

    // 创建模态框内容 - 增大尺寸以容纳更多内容
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content';
    modalContent.style.cssText = `
        background: white;
        padding: 0;
        border-radius: 8px;
        width: 95%;
        max-width: 1200px;
        max-height: 90vh;
        overflow-y: auto;
        position: relative;
    `;

    modalContent.innerHTML = `
        <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #ddd; position: sticky; top: 0; background: white; z-index: 1;">
            <h3>高级编辑产品: ${product.name}</h3>
            <button class="close-modal" style="position: absolute; top: 15px; right: 20px; background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
        </div>
        <form id="advancedEditForm" class="advanced-edit-form">
            <!-- 手风琴式布局 -->
            <div class="accordion-container">
                <!-- GENERAL 手风琴项 -->
                <div class="accordion-item">
                    <div class="accordion-header" onclick="toggleAdvancedEditAccordion('general-content', event)">
                        <h3>GENERAL</h3>
                        <span class="accordion-icon">▼</span>
                    </div>
                    <div class="accordion-content expanded" id="general-content">
                        <div class="general-form-layout">
                            <!-- 第一行：产品名称和产品组 -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-name">产品名称 *</label>
                                    <input type="text" id="edit-name" name="name" value="${product.name || ''}" required>
                                </div>
                                <div class="form-group">
                                    <label for="edit-productGroupId">产品组 *</label>
                                    <select id="edit-productGroupId" name="productGroupId" required>
                                        <option value="">请选择产品组</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 第二行：产品描述 -->
                            <div class="form-group">
                                <label for="edit-description">产品描述</label>
                                <textarea id="edit-description" name="description" rows="3">${product.description || ''}</textarea>
                            </div>

                            <!-- 第三行：成本价格 -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-cost">成本价格 (RM)</label>
                                    <input type="number" id="edit-cost" name="cost" step="0.01" min="0" value="${product.cost || 0}">
                                </div>
                                <div class="form-group">
                                    <!-- 空白占位，保持对齐 -->
                                </div>
                            </div>

                            <!-- 第四行：价格设置组合 -->
                            <div class="form-group">
                                <label>价格设置</label>
                                <div class="price-combination-input">
                                    <div class="price-input-group">
                                        <label>Price</label>
                                        <div class="input-with-controls">
                                            <input type="number" id="edit-price" name="price" step="0.01" min="0" value="${product.price || 0}" placeholder="RM4.70">
                                            <button type="button" class="increment-btn" onclick="adjustPrice('edit-price', 0.1)">+</button>
                                            <button type="button" class="decrement-btn" onclick="adjustPrice('edit-price', -0.1)">-</button>
                                        </div>
                                    </div>

                                    <div class="purchase-option-group">
                                        <select id="edit-purchaseOptions" name="purchaseOptions">
                                            <option value="0" ${(product.purchaseOptions || 0) === 0 ? 'selected' : ''}>And</option>
                                            <option value="1" ${(product.purchaseOptions || 0) === 1 ? 'selected' : ''}>Or</option>
                                        </select>
                                    </div>

                                    <div class="points-input-group">
                                        <label>Points</label>
                                        <div class="input-with-controls">
                                            <input type="number" id="edit-pointsPrice" name="pointsPrice" step="0.01" min="0" value="${product.pointsPrice || 0}" placeholder="Not Set">
                                            <button type="button" class="increment-btn" onclick="adjustPrice('edit-pointsPrice', 0.1)">+</button>
                                            <button type="button" class="decrement-btn" onclick="adjustPrice('edit-pointsPrice', -0.1)">-</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第五行：积分和条码 -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-points">积分</label>
                                    <input type="number" id="edit-points" name="points" min="0" value="${product.points || 0}">
                                </div>
                                <div class="form-group">
                                    <label for="edit-barcode">条码</label>
                                    <input type="text" id="edit-barcode" name="barcode" value="${product.barcode || ''}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- PRICING 手风琴项 -->
                <div class="accordion-item">
                    <div class="accordion-header" onclick="toggleAdvancedEditAccordion('pricing-content', event)">
                        <h3>PRICING</h3>
                        <span class="accordion-icon">▼</span>
                    </div>
                    <div class="accordion-content" id="pricing-content">
                        <table class="user-group-table">
                            <thead>
                                <tr>
                                    <th>启用</th>
                                    <th>用户组名称</th>
                                    <th>价格 (RM)</th>
                                    <th>购买选项</th>
                                    <th>积分价格</th>
                                </tr>
                            </thead>
                            <tbody id="editUserPriceTableBody">
                                <!-- 动态加载用户组 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- RESTRICTIONS 手风琴项 -->
                <div class="accordion-item">
                    <div class="accordion-header" onclick="toggleAdvancedEditAccordion('restrictions-content', event)">
                        <h3>RESTRICTIONS</h3>
                        <span class="accordion-icon">▼</span>
                    </div>
                    <div class="accordion-content" id="restrictions-content">
                        <table class="user-group-table">
                            <thead>
                                <tr>
                                    <th>用户组名称</th>
                                    <th>禁止</th>
                                </tr>
                            </thead>
                            <tbody id="editDisallowedGroupTableBody">
                                <!-- 动态加载用户组 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- STOCK 手风琴项 -->
                <div class="accordion-item">
                    <div class="accordion-header" onclick="toggleAdvancedEditAccordion('stock-content', event)">
                        <h3>STOCK</h3>
                        <span class="accordion-icon">▼</span>
                    </div>
                    <div class="accordion-content" id="stock-content">
                        <div class="form-group">
                            <div class="checkbox-group">
                                <input type="checkbox" id="edit-enableStock" name="enableStock" ${product.enableStock ? 'checked' : ''}>
                                <label for="edit-enableStock">启用库存管理</label>
                            </div>
                        </div>

                        <div class="stock-fields">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-currentStock">当前库存</label>
                                    <input type="number" id="edit-currentStock" name="currentStock" min="0" value="${product.currentStock || 0}">
                                </div>
                                <div class="form-group">
                                    <label for="edit-minStock">最小库存</label>
                                    <input type="number" id="edit-minStock" name="minStock" min="0" value="${product.minStock || 0}">
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="edit-disallowSaleIfOutOfStock" name="disallowSaleIfOutOfStock" ${product.disallowSaleIfOutOfStock ? 'checked' : ''}>
                                    <label for="edit-disallowSaleIfOutOfStock">缺货时禁止销售</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="edit-stockAlert" name="stockAlert" ${product.stockAlert ? 'checked' : ''}>
                                    <label for="edit-stockAlert">启用库存警告</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 按钮组 -->
            <div class="button-group" style="padding: 20px; border-top: 1px solid #ddd; position: sticky; bottom: 0; background: white;">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary save-btn">保存更改</button>
            </div>
        </form>
    `;

    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        /* 手风琴样式 */
        .advanced-edit-form .accordion-container {
            margin: 20px 0;
        }

        .advanced-edit-form .accordion-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
            overflow: hidden;
        }

        .advanced-edit-form .accordion-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            user-select: none;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #ddd;
        }

        .advanced-edit-form .accordion-header:hover {
            background-color: #e9ecef;
        }

        .advanced-edit-form .accordion-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
        }

        .advanced-edit-form .accordion-icon {
            font-size: 12px;
            transition: transform 0.2s ease;
        }

        .advanced-edit-form .accordion-icon.rotated {
            transform: rotate(180deg);
        }

        .advanced-edit-form .accordion-content {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, padding 0.3s ease-out;
        }

        .advanced-edit-form .accordion-content.expanded {
            max-height: 1000px;
            padding: 20px;
            transition: max-height 0.3s ease-in, padding 0.3s ease-in;
        }

        /* GENERAL 表单布局 */
        .advanced-edit-form .general-form-layout {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        /* 统一表单字段样式 */
        .advanced-edit-form .general-form-layout .form-group {
            display: flex;
            flex-direction: column;
        }

        .advanced-edit-form .general-form-layout .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 14px;
        }

        .advanced-edit-form .general-form-layout .form-group input,
        .advanced-edit-form .general-form-layout .form-group select,
        .advanced-edit-form .general-form-layout .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
            height: 40px; /* 统一高度 */
        }

        .advanced-edit-form .general-form-layout .form-group textarea {
            height: 80px; /* 文本域特殊高度 */
            resize: vertical;
        }

        .advanced-edit-form .general-form-layout .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .advanced-edit-form .general-form-layout .form-row .form-group:empty {
            visibility: hidden; /* 隐藏空占位元素但保持布局 */
        }

        /* 表单样式 */
        .advanced-edit-form .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .advanced-edit-form .form-group {
            flex: 1;
        }

        .advanced-edit-form .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .advanced-edit-form .form-group input,
        .advanced-edit-form .form-group select,
        .advanced-edit-form .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .advanced-edit-form .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .advanced-edit-form .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: normal;
        }

        /* 按钮样式 */
        .advanced-edit-form .button-group {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .advanced-edit-form .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .advanced-edit-form .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .advanced-edit-form .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .advanced-edit-form .btn:hover {
            opacity: 0.9;
        }

        /* 表格样式 */
        .advanced-edit-form .user-group-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .advanced-edit-form .user-group-table th {
            background-color: #4a90e2;
            color: white;
            padding: 10px 12px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #4a90e2;
        }

        .advanced-edit-form .user-group-table td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
            background-color: white;
        }

        .advanced-edit-form .user-group-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .advanced-edit-form .user-group-table tr:nth-child(even) td {
            background-color: #f8f9fa;
        }

        .advanced-edit-form .user-group-table input[type="number"],
        .advanced-edit-form .user-group-table select {
            width: 100%;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
            font-size: 13px;
            height: 28px;
        }

        .advanced-edit-form .user-group-table input[type="number"]:disabled,
        .advanced-edit-form .user-group-table select:disabled {
            background-color: #f5f5f5;
            color: #999;
        }

        .advanced-edit-form .user-group-table input[type="checkbox"] {
            width: auto;
            height: auto;
            margin: 0;
        }

        .advanced-edit-form .user-group-table .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* 价格组合输入样式 */
        .advanced-edit-form .price-combination-input {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }

        .advanced-edit-form .price-input-group,
        .advanced-edit-form .points-input-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .advanced-edit-form .purchase-option-group {
            display: flex;
            align-items: center;
        }

        .advanced-edit-form .input-with-controls {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .advanced-edit-form .input-with-controls input {
            width: 80px;
            text-align: center;
        }

        .advanced-edit-form .increment-btn,
        .advanced-edit-form .decrement-btn {
            width: 20px;
            height: 20px;
            padding: 0;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .advanced-edit-form .increment-btn:hover,
        .advanced-edit-form .decrement-btn:hover {
            background-color: #e9ecef;
        }

        .advanced-edit-form .price-combination-input label {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 0;
        }

        /* 图片操作按钮样式 */
        .advanced-edit-form .image-actions {
            display: flex;
            gap: 5px;
        }

        .advanced-edit-form .image-actions button {
            font-size: 11px;
            padding: 2px 6px;
        }

        .advanced-edit-form #editRemoveImageBtn {
            background-color: #dc3545;
            color: white;
            border: none;
        }

        .advanced-edit-form #editRemoveImageBtn:hover {
            background-color: #c82333;
        }
    `;
    document.head.appendChild(style);

    // 初始化功能
    await initializeAdvancedEditModal(product);

    // 事件处理
    const closeModal = () => {
        document.body.removeChild(modalOverlay);
        document.head.removeChild(style);
    };

    // 关闭按钮事件
    modalContent.querySelector('.close-modal').addEventListener('click', closeModal);
    modalContent.querySelector('.cancel-btn').addEventListener('click', closeModal);

    // 点击背景关闭
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            closeModal();
        }
    });

    // 表单提交事件
    modalContent.querySelector('#advancedEditForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await handleAdvancedEditSubmit(product, closeModal);
    });
}

// 处理高级编辑表单提交
async function handleAdvancedEditSubmit(originalProduct, closeModal) {
    const form = document.getElementById('advancedEditForm');
    const formData = new FormData(form);

    // 构建完整的产品数据对象
    const updatedProduct = {
        id: originalProduct.id,
        productGroupId: parseInt(formData.get('productGroupId')),
        name: formData.get('name').trim(),
        description: formData.get('description').trim(),
        price: parseFloat(formData.get('price')) || 0,
        cost: parseFloat(formData.get('cost')) || 0,
        purchaseOptions: parseInt(formData.get('purchaseOptions')) || 0,
        points: parseInt(formData.get('points')) || 0,
        pointsPrice: parseFloat(formData.get('pointsPrice')) || 0,
        barcode: formData.get('barcode').trim(),
        enableStock: formData.has('enableStock'),
        disallowSaleIfOutOfStock: formData.has('disallowSaleIfOutOfStock'),
        stockAlert: formData.has('stockAlert'),
        currentStock: parseInt(formData.get('currentStock')) || 0,
        minStock: parseInt(formData.get('minStock')) || 0,
        isDeleted: originalProduct.isDeleted || false // 保持原有删除状态
    };

    try {
        // 显示加载状态
        const saveBtn = form.querySelector('.save-btn');
        saveBtn.textContent = '保存中...';
        saveBtn.disabled = true;

        // 调用API更新产品
        await updateProduct(updatedProduct);

        // 图片上传功能已移除

        // 处理用户价格更新
        await handleEditUserPricesSubmit(originalProduct.id);

        // 处理禁用用户组更新
        await handleEditDisallowedGroupsSubmit(originalProduct.id);

        // 更新本地缓存
        const index = allProducts.findIndex(p => p.id === originalProduct.id);
        if (index !== -1) {
            allProducts[index] = updatedProduct;
        }

        // 清空产品缓存，强制重新加载
        Object.keys(productCache).forEach(key => {
            delete productCache[key];
        });

        // 关闭模态框
        closeModal();

        // 显示成功消息
        alert('产品更新成功！');

        // 如果当前显示的产品列表包含这个产品，重新渲染
        const currentProductList = document.getElementById('productList');
        if (currentProductList.querySelector(`[data-product-id='${originalProduct.id}']`)) {
            // 重新加载当前显示的产品组
            const expandedButton = document.querySelector('.group-button[data-expanded="true"]');
            if (expandedButton) {
                expandedButton.click();
            }
        }

    } catch (error) {
        console.error('更新产品失败:', error);
        alert('更新产品失败: ' + error.message);

        // 恢复按钮状态
        const saveBtn = form.querySelector('.save-btn');
        saveBtn.textContent = '保存更改';
        saveBtn.disabled = false;
    }
}

// 初始化高级编辑模态框
async function initializeAdvancedEditModal(product) {
    // 加载产品组数据
    await loadProductGroupsForEdit();

    // 设置产品组选择
    const productGroupSelect = document.getElementById('edit-productGroupId');
    if (productGroupSelect && product.productGroupId) {
        productGroupSelect.value = product.productGroupId;
    }

    // 图片功能已移除

    // 初始化用户价格和禁用用户组表格
    await initializeEditUserPriceTable(product.id);
    await initializeEditDisallowedGroupTable(product.id);

    // 添加GENERAL价格变化监听器，实时更新未启用的用户组行
    setupGeneralPriceChangeListeners(product.id);
}

// 加载产品组数据用于编辑
async function loadProductGroupsForEdit() {
    try {
        const data = await fetchProductGroups();
        const productGroups = data.result?.data || data.result || [];
        const select = document.getElementById('edit-productGroupId');

        // 清空现有选项（保留默认选项）
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // 添加产品组选项
        productGroups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.id;
            option.textContent = group.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('加载产品组失败:', error);
    }
}

// 设置编辑模式的图片上传功能
function setupEditImageUpload() {
    const uploadArea = document.getElementById('editImageUploadArea');
    const imageInput = document.getElementById('editImageInput');
    const imagePreview = document.getElementById('editImagePreview');
    const uploadPlaceholder = document.getElementById('editUploadPlaceholder');
    const deleteBtn = document.getElementById('editDeleteImageBtn');
    const removeBtn = document.getElementById('editRemoveImageBtn');

    // 点击上传区域
    uploadArea.addEventListener('click', () => {
        if (!imagePreview.style.display || imagePreview.style.display === 'none') {
            imageInput.click();
        }
    });

    // 文件选择
    imageInput.addEventListener('change', handleEditImageSelect);

    // 拖拽上传
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleEditImageFile(files[0]);
        }
    });

    // 删除图片预览
    deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        clearEditImagePreview();
    });

    // 移除产品图片
    removeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        removeProductImage();
    });
}

// 处理编辑模式的图片选择
function handleEditImageSelect(event) {
    const file = event.target.files[0];
    if (file) {
        handleEditImageFile(file);
    }
}

// 处理编辑模式的图片文件
function handleEditImageFile(file) {
    if (!file.type.startsWith('image/')) {
        alert('请选择图片文件');
        return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB限制
        alert('图片文件大小不能超过5MB');
        return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
        showEditImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);
}

// 显示编辑模式的图片预览
function showEditImagePreview(src, isExistingImage = false) {
    const imagePreview = document.getElementById('editImagePreview');
    const uploadPlaceholder = document.getElementById('editUploadPlaceholder');
    const deleteBtn = document.getElementById('editDeleteImageBtn');
    const removeBtn = document.getElementById('editRemoveImageBtn');

    imagePreview.src = src;
    imagePreview.style.display = 'block';
    uploadPlaceholder.style.display = 'none';
    deleteBtn.style.display = 'block';

    // 如果是现有图片，显示移除按钮
    if (isExistingImage) {
        removeBtn.style.display = 'block';
    } else {
        removeBtn.style.display = 'none';
    }
}

// 清空编辑模式的图片预览
function clearEditImagePreview() {
    const imagePreview = document.getElementById('editImagePreview');
    const uploadPlaceholder = document.getElementById('editUploadPlaceholder');
    const deleteBtn = document.getElementById('editDeleteImageBtn');
    const removeBtn = document.getElementById('editRemoveImageBtn');
    const imageInput = document.getElementById('editImageInput');

    imagePreview.style.display = 'none';
    imagePreview.src = '';
    uploadPlaceholder.style.display = 'block';
    deleteBtn.style.display = 'none';
    removeBtn.style.display = 'none';
    imageInput.value = '';
}

// 加载产品图片
async function loadProductImage(productId) {
    try {
        const response = await apiRequest(`/products/${productId}/image`);
        if (response && response.imageData) {
            const imageUrl = `data:image/jpeg;base64,${response.imageData}`;
            showEditImagePreview(imageUrl, true); // 标记为现有图片
        }
    } catch (error) {
        console.log('没有找到产品图片或加载失败:', error);
        // 不显示错误，因为产品可能没有图片
    }
}

// 处理编辑模式的图片上传
async function handleEditImageUpload(productId) {
    const imagePreview = document.getElementById('editImagePreview');
    if (!productId || !imagePreview.src || imagePreview.style.display === 'none') {
        return;
    }

    try {
        // 将图片转换为base64（去掉data:image/...;base64,前缀）
        const base64Data = imagePreview.src.split(',')[1];

        // 使用PUT请求更新图片
        await updateProductImage({
            id: 0, // 如果是新图片，ID为0
            productId: productId,
            image: base64Data
        });
    } catch (error) {
        console.error('上传图片失败:', error);
        // 不阻止产品更新，只是警告
        alert('产品更新成功，但图片上传失败: ' + error.message);
    }
}

// 初始化编辑模式的用户价格表格
async function initializeEditUserPriceTable(productId) {
    try {
        const [userGroupsData, existingPricesData] = await Promise.all([
            fetchUserGroups(),
            fetchProductUserPrices(productId).catch(() => ({ result: [] }))
        ]);

        const userGroups = userGroupsData.result?.data || userGroupsData.result || [];
        const existingPrices = existingPricesData.result || [];

        // 获取GENERAL中的基础价格
        const basePrice = parseFloat(document.getElementById('edit-price').value) || 0;
        const basePointsPrice = parseFloat(document.getElementById('edit-pointsPrice').value) || 0;
        const basePurchaseOptions = parseInt(document.getElementById('edit-purchaseOptions').value) || 0;

        const tbody = document.getElementById('editUserPriceTableBody');
        tbody.innerHTML = '';

        userGroups.forEach(group => {
            const existingPrice = existingPrices.find(p => p.userGroupId === group.id);
            const row = createEditUserPriceRow(group, existingPrice, basePrice, basePointsPrice, basePurchaseOptions);
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('初始化用户价格表格失败:', error);
    }
}

// 创建编辑模式的用户价格行
function createEditUserPriceRow(userGroup, existingPrice, basePrice, basePointsPrice, basePurchaseOptions) {
    const row = document.createElement('tr');
    const isEnabled = existingPrice && existingPrice.isEnabled;

    // 如果启用了自定义价格，使用自定义价格；否则使用基础价格
    const price = isEnabled ? (existingPrice.price || 0) : (basePrice || 0);
    const pointsPrice = isEnabled ? (existingPrice.pointsPrice || 0) : (basePointsPrice || 0);
    const purchaseOptions = isEnabled ? (existingPrice.purchaseOptions || 0) : (basePurchaseOptions || 0);

    // 格式化价格显示 - 移除不必要的.00
    const formatPrice = (value) => {
        if (!value || value === 0) return '';
        return value % 1 === 0 ? value.toString() : value.toFixed(2);
    };

    const displayPrice = formatPrice(price);
    const displayPointsPrice = formatPrice(pointsPrice);

    row.innerHTML = `
        <td>
            <input type="checkbox" data-user-group-id="${userGroup.id}" data-existing-id="${existingPrice ? existingPrice.id : ''}" ${isEnabled ? 'checked' : ''} onchange="toggleUserPriceRow(this)">
        </td>
        <td>${userGroup.name}</td>
        <td>
            <input type="number" step="0.01" min="0" placeholder="" data-field="price" value="${displayPrice}" ${!isEnabled ? 'disabled' : ''}>
        </td>
        <td>
            <select data-field="purchaseOptions" ${!isEnabled ? 'disabled' : ''}>
                <option value="0" ${purchaseOptions === 0 ? 'selected' : ''}>And</option>
                <option value="1" ${purchaseOptions === 1 ? 'selected' : ''}>Or</option>
            </select>
        </td>
        <td>
            <input type="number" step="1" min="0" placeholder="" data-field="pointsPrice" value="${displayPointsPrice}" ${!isEnabled ? 'disabled' : ''}>
        </td>
    `;
    return row;
}

// 切换用户价格行的启用状态
function toggleUserPriceRow(checkbox) {
    const row = checkbox.closest('tr');
    const inputs = row.querySelectorAll('input[type="number"], select');

    if (checkbox.checked) {
        // 启用输入字段
        inputs.forEach(input => input.disabled = false);
    } else {
        // 禁用输入字段但不清空值，保留现有数据供查看
        inputs.forEach(input => {
            input.disabled = true;
        });

        // 更新为基础价格
        updateRowWithBasePrice(row);
    }
}

// 设置GENERAL价格变化监听器
function setupGeneralPriceChangeListeners(productId) {
    const priceInput = document.getElementById('edit-price');
    const pointsPriceInput = document.getElementById('edit-pointsPrice');
    const purchaseOptionsSelect = document.getElementById('edit-purchaseOptions');

    const updateDisabledRows = () => {
        const tbody = document.getElementById('editUserPriceTableBody');
        const rows = tbody.querySelectorAll('tr');

        rows.forEach(row => {
            const checkbox = row.querySelector('input[type="checkbox"]');
            if (!checkbox.checked) {
                updateRowWithBasePrice(row);
            }
        });
    };

    // 添加事件监听器
    if (priceInput) priceInput.addEventListener('input', updateDisabledRows);
    if (pointsPriceInput) pointsPriceInput.addEventListener('input', updateDisabledRows);
    if (purchaseOptionsSelect) purchaseOptionsSelect.addEventListener('change', updateDisabledRows);
}

// 用基础价格更新行
function updateRowWithBasePrice(row) {
    const basePrice = parseFloat(document.getElementById('edit-price').value) || 0;
    const basePointsPrice = parseFloat(document.getElementById('edit-pointsPrice').value) || 0;
    const basePurchaseOptions = parseInt(document.getElementById('edit-purchaseOptions').value) || 0;

    // 格式化价格显示 - 移除不必要的.00
    const formatPrice = (value) => {
        if (!value || value === 0) return '';
        return value % 1 === 0 ? value.toString() : value.toFixed(2);
    };

    const priceInput = row.querySelector('input[data-field="price"]');
    const pointsPriceInput = row.querySelector('input[data-field="pointsPrice"]');
    const purchaseOptionsSelect = row.querySelector('select[data-field="purchaseOptions"]');

    if (priceInput) priceInput.value = formatPrice(basePrice);
    if (pointsPriceInput) pointsPriceInput.value = formatPrice(basePointsPrice);
    if (purchaseOptionsSelect) purchaseOptionsSelect.value = basePurchaseOptions;
}

// 初始化编辑模式的禁用用户组表格
async function initializeEditDisallowedGroupTable(productId) {
    try {
        const [userGroupsData, existingDisallowedData] = await Promise.all([
            fetchUserGroups(),
            fetchProductDisallowedUserGroups(productId).catch(() => ({ result: [] }))
        ]);

        const userGroups = userGroupsData.result?.data || userGroupsData.result || [];
        const existingDisallowed = existingDisallowedData.result || [];

        const tbody = document.getElementById('editDisallowedGroupTableBody');
        tbody.innerHTML = '';

        userGroups.forEach(group => {
            const isDisallowed = existingDisallowed.some(d => d.userGroupId === group.id && d.isDisallowed);
            const existingRecord = existingDisallowed.find(d => d.userGroupId === group.id);

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${group.name}</td>
                <td>
                    <input type="checkbox" data-user-group-id="${group.id}" data-existing-id="${existingRecord ? existingRecord.id : ''}" ${isDisallowed ? 'checked' : ''}>
                </td>
            `;
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('初始化禁用用户组表格失败:', error);
    }
}

// 处理编辑模式的用户价格提交
async function handleEditUserPricesSubmit(productId) {
    const userPriceRows = document.querySelectorAll('#editUserPriceTableBody tr');

    for (const row of userPriceRows) {
        const checkbox = row.querySelector('input[type="checkbox"]');
        const userGroupId = parseInt(checkbox.dataset.userGroupId);
        const existingId = checkbox.dataset.existingId;
        const priceInput = row.querySelector('input[data-field="price"]');
        const pointsPriceInput = row.querySelector('input[data-field="pointsPrice"]');
        const purchaseOptionsSelect = row.querySelector('select[data-field="purchaseOptions"]');

        const price = parseFloat(priceInput.value) || 0;
        const pointsPrice = parseFloat(pointsPriceInput.value) || 0;
        const purchaseOptions = parseInt(purchaseOptionsSelect.value) || 0;
        const isEnabled = checkbox.checked && (price > 0 || pointsPrice > 0);

        try {
            if (existingId) {
                // 更新现有记录
                await updateProductUserPrice({
                    id: parseInt(existingId),
                    productId: productId,
                    userGroupId: userGroupId,
                    price: price,
                    pointsPrice: pointsPrice,
                    purchaseOptions: purchaseOptions,
                    isEnabled: isEnabled
                });
            } else if (isEnabled) {
                // 创建新记录
                await createProductUserPrice(productId, {
                    userGroupId: userGroupId,
                    price: price,
                    pointsPrice: pointsPrice,
                    purchaseOptions: purchaseOptions,
                    isEnabled: true
                });
            }
        } catch (error) {
            console.error('更新用户价格失败:', error);
            // 不阻止整个更新过程，只记录错误
        }
    }
}

// 处理编辑模式的禁用用户组提交
async function handleEditDisallowedGroupsSubmit(productId) {
    const disallowedRows = document.querySelectorAll('#editDisallowedGroupTableBody tr');

    for (const row of disallowedRows) {
        const checkbox = row.querySelector('input[type="checkbox"]');
        const userGroupId = parseInt(checkbox.dataset.userGroupId);
        const existingId = checkbox.dataset.existingId;
        const isDisallowed = checkbox.checked;

        try {
            if (existingId) {
                // 更新现有记录
                await updateDisallowedUserGroup({
                    id: parseInt(existingId),
                    productId: productId,
                    userGroupId: userGroupId,
                    isDisallowed: isDisallowed
                });
            } else if (isDisallowed) {
                // 创建新记录
                await createDisallowedUserGroup(productId, {
                    userGroupId: userGroupId,
                    isDisallowed: true
                });
            }
        } catch (error) {
            console.error('更新禁用用户组失败:', error);
            // 不阻止整个更新过程，只记录错误
        }
    }
}

// 调整价格值
function adjustPrice(inputId, amount) {
    const input = document.getElementById(inputId);
    const currentValue = parseFloat(input.value) || 0;
    const newValue = Math.max(0, currentValue + amount);
    input.value = newValue.toFixed(2);
}

// 移除产品图片
async function removeProductImage() {
    if (confirm('确定要移除这个产品图片吗？')) {
        try {
            // 这里需要调用删除图片的API
            // 暂时先清空预览
            clearEditImagePreview();
            alert('产品图片已移除');
        } catch (error) {
            console.error('移除产品图片失败:', error);
            alert('移除产品图片失败: ' + error.message);
        }
    }
}

// 手风琴切换功能
function toggleAdvancedEditAccordion(contentId, event) {
    const content = document.getElementById(contentId);
    const header = event.target.closest('.accordion-header');
    const icon = header.querySelector('.accordion-icon');

    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        icon.classList.remove('rotated');
    } else {
        content.classList.add('expanded');
        icon.classList.add('rotated');
    }
}

