// 初始化
document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('loginForm');
    form.addEventListener('submit', handleLogin);

    // 回车键登入
    document.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleLogin(e);
        }
    });
});

async function handleLogin(event) {
    event.preventDefault();

    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();
    const errorMsg = document.getElementById('error');
    const loading = document.getElementById('loading');
    const button = document.getElementById('loginButton');

    // 清除之前的错误信息
    errorMsg.style.display = 'none';
    errorMsg.textContent = '';

    if (!username || !password) {
        showError('请输入用户名和密码');
        return;
    }

    // 显示加载状态
    loading.style.display = 'block';
    button.disabled = true;
    button.textContent = '登入中...';

    try {
        const data = await loginUser(username, password);
        const token = data?.result?.token;
        if (token) {
            localStorage.setItem('token', 'Bearer ' + token);

            // 成功动画
            button.textContent = '登入成功！';
            button.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';

            setTimeout(() => {
                window.location.href = 'product.html';
            }, 1000);
        } else {
            showError('登入失败：无有效 token');
        }
    } catch (err) {
        if (err.message.includes('401')) {
            showError('用户名或密码错误');
        } else if (err.message.includes('403')) {
            showError('账户被禁用，请联系管理员');
        } else {
            showError('网络错误：' + err.message);
        }
    } finally {
        // 恢复按钮状态
        loading.style.display = 'none';
        button.disabled = false;
        if (button.textContent !== '登入成功！') {
            button.textContent = '登入';
        }
    }
}

function showError(message) {
    const errorMsg = document.getElementById('error');
    errorMsg.textContent = message;
    errorMsg.style.display = 'block';

    // 错误动画
    errorMsg.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
        errorMsg.style.animation = '';
    }, 500);
}

// 添加震动动画
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);

// 兼容旧的login函数
async function login() {
    handleLogin(new Event('submit'));
}