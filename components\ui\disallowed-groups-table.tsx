'use client';

import React, { useState, useCallback } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { UserGroup } from '@/types/product';

interface DisallowedGroupRow {
  userGroupId: number;
  userGroupName: string;
  isDisallowed: boolean;
}

interface DisallowedGroupsTableProps {
  userGroups: UserGroup[];
  onDisallowedGroupsChange: (disallowedGroups: DisallowedGroupRow[]) => void;
  initialDisallowedGroups?: DisallowedGroupRow[];
  className?: string;
}

export function DisallowedGroupsTable({ 
  userGroups, 
  onDisallowedGroupsChange, 
  initialDisallowedGroups = [],
  className 
}: DisallowedGroupsTableProps) {
  const [disallowedGroups, setDisallowedGroups] = useState<DisallowedGroupRow[]>(() => {
    // 初始化所有用户组的禁止状态
    return userGroups.map(group => {
      const existingDisallowed = initialDisallowedGroups.find(d => d.userGroupId === group.id);
      return existingDisallowed || {
        userGroupId: group.id,
        userGroupName: group.name,
        isDisallowed: false
      };
    });
  });

  const toggleDisallowed = useCallback((userGroupId: number, isDisallowed: boolean) => {
    const updatedGroups = disallowedGroups.map(group => {
      if (group.userGroupId === userGroupId) {
        return { ...group, isDisallowed };
      }
      return group;
    });
    
    setDisallowedGroups(updatedGroups);
    onDisallowedGroupsChange(updatedGroups);
  }, [disallowedGroups, onDisallowedGroupsChange]);

  return (
    <div className={className}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>用户组</TableHead>
            <TableHead className="w-24 text-center">禁止购买</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {disallowedGroups.map((group) => (
            <TableRow key={group.userGroupId}>
              <TableCell className="font-medium">
                {group.userGroupName}
              </TableCell>
              <TableCell className="text-center">
                <Checkbox
                  checked={group.isDisallowed}
                  onCheckedChange={(checked) => 
                    toggleDisallowed(group.userGroupId, checked as boolean)
                  }
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      <div className="mt-4 text-sm text-gray-600">
        已禁止 {disallowedGroups.filter(g => g.isDisallowed).length} 个用户组购买此产品
      </div>
    </div>
  );
}
