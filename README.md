# 产品添加页面 - Next.js + shadcn/ui 版本

这是一个将原有的 HTML/CSS/JS 产品添加页面转换为现代化 Next.js + TypeScript + shadcn/ui 组件的完整实现。

## 🚀 功能特性

### ✅ 已实现功能

- **现代化 UI**: 使用 shadcn/ui 组件库，提供一致的设计系统
- **TypeScript 支持**: 完整的类型安全，包括 API 响应和表单数据
- **响应式设计**: 使用 Tailwind CSS 实现移动端友好的布局
- **表单验证**: 使用 React Hook Form + Zod 进行客户端验证
- **手风琴布局**: 使用 shadcn/ui Accordion 组件实现折叠式表单布局
- **图片上传**: 支持拖拽上传和预览功能
- **用户价格管理**: 动态用户组价格设置表格
- **时间网格选择**: 可视化的时间范围选择器
- **库存管理**: 完整的库存设置和警告功能
- **状态持久化**: 表单状态自动保存到 localStorage
- **错误处理**: 友好的错误提示和验证消息
- **加载状态**: 优雅的加载指示器和成功提示

### 🎯 核心组件

1. **AddProductPage** - 主页面组件
2. **ProductFormSections** - 手风琴式表单布局
3. **ImageUpload** - 图片上传组件
4. **UserPriceTable** - 用户价格设置表格
5. **DisallowedGroupsTable** - 禁止用户组表格
6. **TimeGrid** - 时间范围选择网格
7. **useProductForm** - 表单状态管理 Hook

## 📁 项目结构

```
├── app/
│   └── products/
│       └── add/
│           └── page.tsx              # Next.js 页面路由
├── components/
│   ├── forms/
│   │   └── product-form-fields.tsx   # 表单字段组件
│   ├── pages/
│   │   └── add-product-page.tsx      # 主页面组件
│   └── ui/
│       ├── image-upload.tsx          # 图片上传组件
│       ├── time-grid.tsx             # 时间网格组件
│       ├── user-price-table.tsx      # 用户价格表格
│       ├── disallowed-groups-table.tsx # 禁止用户组表格
│       ├── product-form-sections.tsx # 手风琴布局
│       ├── table.tsx                 # 表格组件
│       ├── alert.tsx                 # 警告组件
│       └── card.tsx                  # 卡片组件
├── hooks/
│   └── use-product-form.ts           # 表单状态管理
├── lib/
│   ├── api.ts                        # API 服务层
│   ├── utils.ts                      # 工具函数
│   └── validations/
│       └── product.ts                # 表单验证 Schema
├── types/
│   └── product.ts                    # TypeScript 类型定义
└── package-dependencies.md           # 依赖安装指南
```

## 🛠️ 安装和设置

### 1. 安装依赖

参考 `package-dependencies.md` 文件安装所有必需的依赖包。

### 2. 配置 Tailwind CSS

确保 `tailwind.config.js` 和 `globals.css` 按照文档配置。

### 3. 设置 shadcn/ui

```bash
npx shadcn-ui@latest init
```

然后安装所需的组件：

```bash
npx shadcn-ui@latest add button input textarea select checkbox form accordion table alert card
```

### 4. 环境配置

确保 API 基础 URL 在 `lib/api.ts` 中正确配置：

```typescript
const API_BASE_URL = 'http://gizmodelphine.ddns.net:8081/api/v2.0';
```

## 🎨 设计系统

### 颜色主题
- **主色**: Blue (shadcn/ui 默认)
- **成功色**: Green
- **错误色**: Red
- **警告色**: Yellow

### 组件规范
- 使用 shadcn/ui 组件保持一致性
- 遵循 Tailwind CSS 设计原则
- 响应式设计优先

## 📱 响应式设计

- **移动端**: 单列布局，折叠式导航
- **平板**: 两列布局，适配中等屏幕
- **桌面**: 三列布局，完整功能展示

## 🔧 API 集成

### 支持的 API 端点

- `POST /products` - 创建产品
- `POST /products/{id}/images` - 上传产品图片
- `POST /products/{id}/userprices` - 创建用户价格
- `POST /products/{id}/disallowedusergroups` - 创建禁止用户组
- `GET /productgroups` - 获取产品组
- `GET /usergroups` - 获取用户组

### 错误处理

- 网络错误自动重试
- 友好的错误消息显示
- 表单字段级别的验证错误

## 🚦 使用方法

### 基本使用

```tsx
import { AddProductPage } from '@/components/pages/add-product-page';

export default function ProductAddPage() {
  return (
    <AddProductPage 
      onSuccess={() => {
        // 产品创建成功后的回调
        console.log('产品创建成功');
      }}
    />
  );
}
```

### 自定义配置

```tsx
const customOptions = {
  onSuccess: (product) => {
    router.push(`/products/${product.id}`);
  },
  onError: (error) => {
    console.error('创建失败:', error);
  }
};
```

## 🧪 测试建议

### 单元测试
- 表单验证逻辑
- API 服务函数
- 工具函数

### 集成测试
- 表单提交流程
- 图片上传功能
- 状态持久化

### E2E 测试
- 完整的产品创建流程
- 错误处理场景
- 响应式布局

## 🔄 从原版本迁移

### 主要变化

1. **技术栈升级**:
   - HTML/CSS/JS → Next.js/TypeScript/React
   - 原生表单 → React Hook Form + Zod
   - 原生样式 → Tailwind CSS + shadcn/ui

2. **架构改进**:
   - 组件化设计
   - 类型安全
   - 状态管理优化
   - 错误处理增强

3. **用户体验提升**:
   - 更好的加载状态
   - 实时表单验证
   - 响应式设计
   - 无障碍访问支持

## 📈 性能优化

- 组件懒加载
- 图片压缩和优化
- API 请求缓存
- 表单状态本地存储

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🆘 支持

如有问题或建议，请创建 Issue 或联系开发团队。
