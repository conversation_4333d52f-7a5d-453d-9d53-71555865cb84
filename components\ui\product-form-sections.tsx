'use client';

import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Card, CardContent } from '@/components/ui/card';

interface ProductFormSectionProps {
  children: React.ReactNode;
  className?: string;
}

export function ProductFormSections({ children, className }: ProductFormSectionProps) {
  return (
    <Card className={className}>
      <CardContent className="p-0">
        <Accordion type="multiple" defaultValue={['general']} className="w-full">
          {children}
        </Accordion>
      </CardContent>
    </Card>
  );
}

interface ProductFormSectionItemProps {
  value: string;
  title: string;
  children: React.ReactNode;
  className?: string;
}

export function ProductFormSectionItem({ 
  value, 
  title, 
  children, 
  className 
}: ProductFormSectionItemProps) {
  return (
    <AccordionItem value={value} className={className}>
      <AccordionTrigger className="px-6 py-4 text-left hover:no-underline">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </AccordionTrigger>
      <AccordionContent className="px-6 pb-6">
        {children}
      </AccordionContent>
    </AccordionItem>
  );
}

// 预定义的表单部分组件
export function GeneralSection({ children }: { children: React.ReactNode }) {
  return (
    <ProductFormSectionItem value="general" title="GENERAL">
      {children}
    </ProductFormSectionItem>
  );
}

export function PricingSection({ children }: { children: React.ReactNode }) {
  return (
    <ProductFormSectionItem value="pricing" title="PRICING">
      {children}
    </ProductFormSectionItem>
  );
}

export function RestrictionsSection({ children }: { children: React.ReactNode }) {
  return (
    <ProductFormSectionItem value="restrictions" title="RESTRICTIONS">
      {children}
    </ProductFormSectionItem>
  );
}

export function AvailabilitySection({ children }: { children: React.ReactNode }) {
  return (
    <ProductFormSectionItem value="availability" title="AVAILABILITY">
      {children}
    </ProductFormSectionItem>
  );
}

export function StockSection({ children }: { children: React.ReactNode }) {
  return (
    <ProductFormSectionItem value="stock" title="STOCK">
      {children}
    </ProductFormSectionItem>
  );
}
