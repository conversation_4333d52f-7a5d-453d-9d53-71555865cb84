import { z } from 'zod';

export const productFormSchema = z.object({
  name: z.string().min(1, '产品名称不能为空').max(255, '产品名称不能超过255个字符'),
  description: z.string().optional(),
  productGroupId: z.string().min(1, '请选择产品组'),
  price: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0;
  }, '价格必须是非负数'),
  cost: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0;
  }, '成本必须是非负数'),
  points: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    const num = parseInt(val);
    return !isNaN(num) && num >= 0;
  }, '积分必须是非负整数'),
  pointsPrice: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    const num = parseFloat(val);
    return !isNaN(num) && num >= 0;
  }, '积分价格必须是非负数'),
  barcode: z.string().optional(),
  productType: z.string().optional(),
  displayOrder: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    const num = parseInt(val);
    return !isNaN(num) && num >= 0;
  }, '显示顺序必须是非负整数'),
  purchaseOptions: z.string().optional(),
  enableStock: z.boolean().default(true),
  stockAlertThreshold: z.string().optional().refine((val) => {
    if (!val || val === '') return true;
    const num = parseInt(val);
    return !isNaN(num) && num >= 0;
  }, '库存警告阈值必须是非负整数'),
  disallowSaleIfOutOfStock: z.boolean().default(false),
  stockAlert: z.boolean().default(false),
  disallowClientOrder: z.boolean().default(false),
  restrictGuestSale: z.boolean().default(false),
  restrictSale: z.boolean().default(false),
  timeRange: z.boolean().default(false),
});

export type ProductFormData = z.infer<typeof productFormSchema>;

// 用户价格验证 schema
export const userPriceSchema = z.object({
  userGroupId: z.number().min(1, '请选择用户组'),
  price: z.number().min(0, '价格不能为负数').optional(),
  pointsPrice: z.number().min(0, '积分价格不能为负数').optional(),
  purchaseOptions: z.number().min(0).max(1).default(0),
  isEnabled: z.boolean().default(true),
});

export type UserPriceData = z.infer<typeof userPriceSchema>;

// 禁止用户组验证 schema
export const disallowedUserGroupSchema = z.object({
  userGroupId: z.number().min(1, '请选择用户组'),
  isDisallowed: z.boolean().default(true),
});

export type DisallowedUserGroupData = z.infer<typeof disallowedUserGroupSchema>;

// 时间槽验证 schema
export const timeSlotSchema = z.object({
  day: z.string(),
  hour: z.number().min(0).max(23),
  selected: z.boolean().default(false),
});

export type TimeSlotData = z.infer<typeof timeSlotSchema>;
