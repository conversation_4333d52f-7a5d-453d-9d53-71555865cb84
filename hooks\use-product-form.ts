'use client';

import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import { 
  productFormSchema, 
  ProductFormData,
  UserPriceData,
  DisallowedUserGroupData 
} from '@/lib/validations/product';
import { 
  createProduct, 
  uploadProductImage, 
  createProductUserPrice, 
  createDisallowedUserGroup,
  fetchProductGroups,
  fetchUserGroups
} from '@/lib/api';
import { 
  ProductGroup, 
  UserGroup, 
  Product,
  TimeSlot,
  LoadingState,
  SuccessMessage 
} from '@/types/product';

interface UseProductFormOptions {
  onSuccess?: (product: Product) => void;
  onError?: (error: Error) => void;
}

export function useProductForm(options: UseProductFormOptions = {}) {
  const [productGroups, setProductGroups] = useState<ProductGroup[]>([]);
  const [userGroups, setUserGroups] = useState<UserGroup[]>([]);
  const [currentProductId, setCurrentProductId] = useState<number | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [userPrices, setUserPrices] = useState<any[]>([]);
  const [disallowedGroups, setDisallowedGroups] = useState<any[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState<LoadingState>({ isLoading: false });
  const [successMessage, setSuccessMessage] = useState<SuccessMessage>({ show: false, message: '' });

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      name: '',
      description: '',
      productGroupId: '',
      price: '',
      cost: '',
      points: '',
      pointsPrice: '',
      barcode: '',
      productType: '0',
      displayOrder: '',
      purchaseOptions: '0',
      enableStock: true,
      stockAlertThreshold: '',
      disallowSaleIfOutOfStock: false,
      stockAlert: false,
      disallowClientOrder: false,
      restrictGuestSale: false,
      restrictSale: false,
      timeRange: false,
    },
  });

  // 加载初始数据
  const loadInitialData = useCallback(async () => {
    try {
      setLoading({ isLoading: true, message: '加载数据中...' });
      
      const [productGroupsData, userGroupsData] = await Promise.all([
        fetchProductGroups(),
        fetchUserGroups()
      ]);

      const groups = productGroupsData.result?.data || productGroupsData.result || [];
      const users = userGroupsData.result?.data || userGroupsData.result || [];
      
      setProductGroups(groups);
      setUserGroups(users);
      
      // 初始化用户价格和禁止用户组
      setUserPrices(users.map(user => ({
        userGroupId: user.id,
        userGroupName: user.name,
        enabled: false,
        price: '',
        purchaseOptions: '0',
        pointsPrice: ''
      })));
      
      setDisallowedGroups(users.map(user => ({
        userGroupId: user.id,
        userGroupName: user.name,
        isDisallowed: false
      })));
      
    } catch (error) {
      console.error('加载初始数据失败:', error);
      toast.error('加载数据失败，请刷新页面重试');
    } finally {
      setLoading({ isLoading: false });
    }
  }, []);

  // 处理图片选择
  const handleImageSelect = useCallback((file: File) => {
    setImageFile(file);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  // 处理图片移除
  const handleImageRemove = useCallback(() => {
    setImageFile(null);
    setImagePreview('');
  }, []);

  // 构建产品数据
  const buildProductData = useCallback((formData: ProductFormData): Partial<Product> => {
    const productGroupId = parseInt(formData.productGroupId);
    const name = formData.name.trim();

    if (!productGroupId || !name) {
      throw new Error('产品组ID和产品名称是必需的');
    }

    const productData: Partial<Product> = {
      productGroupId: productGroupId,
      name: name,
      description: formData.description?.trim() || "",
      price: formData.price ? parseFloat(formData.price) : 0,
      cost: formData.cost ? parseFloat(formData.cost) : 0,
      isDeleted: false
    };

    // 添加可选字段
    const productType = parseInt(formData.productType || '0');
    if (productType) {
      productData.productType = productType;
      productData.isService = productType === 1;
    }

    const points = parseInt(formData.points || '0');
    if (points) {
      productData.points = points;
    }

    const pointsPrice = parseFloat(formData.pointsPrice || '0');
    if (pointsPrice) {
      productData.pointsPrice = pointsPrice;
    }

    const barcode = formData.barcode?.trim();
    if (barcode) {
      productData.barcode = barcode;
    }

    const displayOrder = parseInt(formData.displayOrder || '0');
    if (displayOrder) {
      productData.displayOrder = displayOrder;
    }

    const purchaseOptions = parseInt(formData.purchaseOptions || '0');
    if (purchaseOptions !== undefined && purchaseOptions !== null) {
      productData.purchaseOptions = purchaseOptions;
    }

    // 布尔字段
    if (formData.enableStock) {
      productData.enableStock = true;
    }

    if (formData.disallowClientOrder) {
      productData.disallowClientOrder = true;
    }

    if (formData.restrictGuestSale) {
      productData.restrictGuestSale = true;
    }

    if (formData.restrictSale) {
      productData.restrictSale = true;
    }

    if (formData.stockAlert) {
      productData.stockAlert = true;
      const threshold = parseInt(formData.stockAlertThreshold || '0');
      if (threshold) {
        productData.stockAlertThreshold = threshold;
      }
    }

    if (formData.disallowSaleIfOutOfStock) {
      productData.disallowSaleIfOutOfStock = true;
    }

    return productData;
  }, []);

  // 处理图片上传
  const handleImageUpload = useCallback(async () => {
    if (!currentProductId || !imagePreview) {
      return;
    }

    try {
      // 将图片转换为base64（去掉data:image/...;base64,前缀）
      const base64Data = imagePreview.split(',')[1];
      await uploadProductImage(currentProductId, base64Data);
    } catch (error) {
      console.error('上传图片失败:', error);
      toast.error('产品创建成功，但图片上传失败: ' + (error as Error).message);
    }
  }, [currentProductId, imagePreview]);

  // 处理用户价格提交
  const handleUserPricesSubmit = useCallback(async () => {
    if (!currentProductId) return;

    for (const userPrice of userPrices) {
      if (!userPrice.enabled) continue;

      const price = parseFloat(userPrice.price) || 0;
      const pointsPrice = parseFloat(userPrice.pointsPrice) || 0;
      const purchaseOptions = parseInt(userPrice.purchaseOptions) || 0;

      if (price > 0 || pointsPrice > 0) {
        try {
          await createProductUserPrice(currentProductId, {
            userGroupId: userPrice.userGroupId,
            price: price,
            pointsPrice: pointsPrice,
            purchaseOptions: purchaseOptions,
            isEnabled: true
          });
        } catch (error) {
          console.error('创建用户价格失败:', error);
          toast.error(`为用户组设置价格失败: ${(error as Error).message}`);
        }
      }
    }
  }, [currentProductId, userPrices]);

  // 处理禁止用户组提交
  const handleDisallowedGroupsSubmit = useCallback(async () => {
    if (!currentProductId) return;

    for (const group of disallowedGroups) {
      if (!group.isDisallowed) continue;

      try {
        await createDisallowedUserGroup(currentProductId, {
          userGroupId: group.userGroupId,
          isDisallowed: true
        });
      } catch (error) {
        console.error('创建禁止用户组失败:', error);
        toast.error(`设置禁止用户组失败: ${(error as Error).message}`);
      }
    }
  }, [currentProductId, disallowedGroups]);

  // 显示成功消息
  const showSuccessMessage = useCallback((message: string = '产品添加成功！') => {
    setSuccessMessage({ show: true, message });
    setTimeout(() => {
      setSuccessMessage({ show: false, message: '' });
    }, 3000);
  }, []);

  // 保存表单状态到 localStorage
  const saveFormState = useCallback(() => {
    const formData = form.getValues();
    const state = {
      ...formData,
      userPrices,
      disallowedGroups,
      timeSlots
    };
    localStorage.setItem('productFormState', JSON.stringify(state));
  }, [form, userPrices, disallowedGroups, timeSlots]);

  // 从 localStorage 恢复表单状态
  const restoreFormState = useCallback(() => {
    const savedState = localStorage.getItem('productFormState');
    if (!savedState) return;

    try {
      const state = JSON.parse(savedState);
      
      // 恢复表单字段（除了产品名称）
      const { name, ...restState } = state;
      form.reset(restState);
      
      // 恢复其他状态
      if (state.userPrices) {
        setUserPrices(state.userPrices);
      }
      if (state.disallowedGroups) {
        setDisallowedGroups(state.disallowedGroups);
      }
      if (state.timeSlots) {
        setTimeSlots(state.timeSlots);
      }
    } catch (error) {
      console.error('恢复表单状态失败:', error);
    }
  }, [form]);

  // 表单提交处理
  const onSubmit = useCallback(async (data: ProductFormData) => {
    try {
      setLoading({ isLoading: true, message: '正在添加产品...' });

      // 构建产品数据
      const productData = buildProductData(data);
      console.log('发送的产品数据:', JSON.stringify(productData, null, 2));

      // 调用API创建产品
      const result = await createProduct(productData);
      console.log('API返回结果:', result);
      const productId = result.result?.id || result.id;
      setCurrentProductId(productId);

      // 处理图片上传
      await handleImageUpload();

      // 处理用户价格设置
      await handleUserPricesSubmit();

      // 处理禁止用户组设置
      await handleDisallowedGroupsSubmit();

      // 保存当前表单状态
      saveFormState();

      // 显示成功消息
      showSuccessMessage();

      // 只清空产品名称，保留其他设置
      form.setValue('name', '');
      handleImageRemove();

      // 调用成功回调
      if (options.onSuccess && result.result) {
        options.onSuccess(result.result);
      }

    } catch (error) {
      console.error('创建产品失败:', error);
      
      const errorMessage = (error as Error).message;
      
      // 处理特定的错误类型
      if (errorMessage.includes('Name value') && errorMessage.includes('is not unique')) {
        form.setError('name', { message: '产品名称已存在，请使用不同的名称' });
      } else if (errorMessage.includes('Barcode') && errorMessage.includes('is not unique')) {
        form.setError('barcode', { message: '条码已存在，请使用不同的条码' });
      } else if (errorMessage.includes('ProductGroupId') && errorMessage.includes('not found')) {
        form.setError('productGroupId', { message: '选择的产品组无效，请重新选择' });
      } else if (errorMessage.includes('400')) {
        toast.error('数据验证失败: 请检查输入的数据格式是否正确');
      } else {
        toast.error('创建产品失败: ' + errorMessage);
      }

      // 调用错误回调
      if (options.onError) {
        options.onError(error as Error);
      }
    } finally {
      setLoading({ isLoading: false });
    }
  }, [
    buildProductData,
    handleImageUpload,
    handleUserPricesSubmit,
    handleDisallowedGroupsSubmit,
    saveFormState,
    showSuccessMessage,
    form,
    handleImageRemove,
    options
  ]);

  // 初始化
  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  return {
    form,
    productGroups,
    userGroups,
    imageFile,
    imagePreview,
    userPrices,
    disallowedGroups,
    timeSlots,
    loading,
    successMessage,
    onSubmit: form.handleSubmit(onSubmit),
    handleImageSelect,
    handleImageRemove,
    setUserPrices,
    setDisallowedGroups,
    setTimeSlots,
    restoreFormState,
    saveFormState,
  };
}
