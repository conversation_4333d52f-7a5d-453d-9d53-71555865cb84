'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface TimeSlot {
  day: string;
  hour: number;
  selected: boolean;
}

interface TimeGridProps {
  onSelectionChange: (selectedSlots: TimeSlot[]) => void;
  initialSelection?: TimeSlot[];
  className?: string;
}

const DAYS = [
  { key: 'Sunday', label: '周日' },
  { key: 'Monday', label: '周一' },
  { key: 'Tuesday', label: '周二' },
  { key: 'Wednesday', label: '周三' },
  { key: 'Thursday', label: '周四' },
  { key: 'Friday', label: '周五' },
  { key: 'Saturday', label: '周六' },
];

const HOURS = Array.from({ length: 24 }, (_, i) => i);

export function TimeGrid({ onSelectionChange, initialSelection = [], className }: TimeGridProps) {
  const [selectedSlots, setSelectedSlots] = useState<Set<string>>(() => {
    const initialSet = new Set<string>();
    initialSelection.forEach(slot => {
      initialSet.add(`${slot.day}-${slot.hour}`);
    });
    return initialSet;
  });

  const toggleSlot = useCallback((day: string, hour: number) => {
    const slotKey = `${day}-${hour}`;
    const newSelectedSlots = new Set(selectedSlots);
    
    if (newSelectedSlots.has(slotKey)) {
      newSelectedSlots.delete(slotKey);
    } else {
      newSelectedSlots.add(slotKey);
    }
    
    setSelectedSlots(newSelectedSlots);
    
    // 转换为 TimeSlot 数组并调用回调
    const timeSlots: TimeSlot[] = [];
    DAYS.forEach(dayInfo => {
      HOURS.forEach(hourValue => {
        timeSlots.push({
          day: dayInfo.key,
          hour: hourValue,
          selected: newSelectedSlots.has(`${dayInfo.key}-${hourValue}`)
        });
      });
    });
    
    onSelectionChange(timeSlots);
  }, [selectedSlots, onSelectionChange]);

  const selectAllForDay = useCallback((day: string) => {
    const newSelectedSlots = new Set(selectedSlots);
    HOURS.forEach(hour => {
      newSelectedSlots.add(`${day}-${hour}`);
    });
    setSelectedSlots(newSelectedSlots);
    
    const timeSlots: TimeSlot[] = [];
    DAYS.forEach(dayInfo => {
      HOURS.forEach(hourValue => {
        timeSlots.push({
          day: dayInfo.key,
          hour: hourValue,
          selected: newSelectedSlots.has(`${dayInfo.key}-${hourValue}`)
        });
      });
    });
    
    onSelectionChange(timeSlots);
  }, [selectedSlots, onSelectionChange]);

  const clearAllForDay = useCallback((day: string) => {
    const newSelectedSlots = new Set(selectedSlots);
    HOURS.forEach(hour => {
      newSelectedSlots.delete(`${day}-${hour}`);
    });
    setSelectedSlots(newSelectedSlots);
    
    const timeSlots: TimeSlot[] = [];
    DAYS.forEach(dayInfo => {
      HOURS.forEach(hourValue => {
        timeSlots.push({
          day: dayInfo.key,
          hour: hourValue,
          selected: newSelectedSlots.has(`${dayInfo.key}-${hourValue}`)
        });
      });
    });
    
    onSelectionChange(timeSlots);
  }, [selectedSlots, onSelectionChange]);

  const clearAll = useCallback(() => {
    setSelectedSlots(new Set());
    
    const timeSlots: TimeSlot[] = [];
    DAYS.forEach(dayInfo => {
      HOURS.forEach(hourValue => {
        timeSlots.push({
          day: dayInfo.key,
          hour: hourValue,
          selected: false
        });
      });
    });
    
    onSelectionChange(timeSlots);
  }, [onSelectionChange]);

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex justify-between items-center">
        <h4 className="text-sm font-medium text-gray-700">时间范围选择</h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={clearAll}
        >
          清空所有
        </Button>
      </div>
      
      <div className="border rounded-lg overflow-hidden">
        {/* 表头 */}
        <div className="grid grid-cols-25 bg-gray-50 border-b">
          <div className="p-2 text-xs font-medium text-gray-600 border-r"></div>
          {HOURS.map(hour => (
            <div
              key={hour}
              className="p-2 text-xs font-medium text-gray-600 text-center border-r last:border-r-0"
            >
              {hour.toString().padStart(2, '0')}
            </div>
          ))}
        </div>
        
        {/* 时间网格 */}
        {DAYS.map((dayInfo, dayIndex) => (
          <div key={dayInfo.key} className="grid grid-cols-25 border-b last:border-b-0">
            <div className="p-2 bg-gray-50 border-r flex items-center justify-between">
              <span className="text-xs font-medium text-gray-700">{dayInfo.label}</span>
              <div className="flex gap-1">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-5 px-1 text-xs"
                  onClick={() => selectAllForDay(dayInfo.key)}
                >
                  全选
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-5 px-1 text-xs"
                  onClick={() => clearAllForDay(dayInfo.key)}
                >
                  清空
                </Button>
              </div>
            </div>
            {HOURS.map(hour => {
              const slotKey = `${dayInfo.key}-${hour}`;
              const isSelected = selectedSlots.has(slotKey);
              
              return (
                <button
                  key={hour}
                  type="button"
                  className={cn(
                    'p-1 border-r last:border-r-0 hover:bg-gray-100 transition-colors',
                    'min-h-[32px] text-xs',
                    isSelected && 'bg-primary text-primary-foreground hover:bg-primary/90'
                  )}
                  onClick={() => toggleSlot(dayInfo.key, hour)}
                >
                  {isSelected && '✓'}
                </button>
              );
            })}
          </div>
        ))}
      </div>
      
      <div className="text-xs text-gray-500">
        已选择 {selectedSlots.size} 个时间段
      </div>
    </div>
  );
}
